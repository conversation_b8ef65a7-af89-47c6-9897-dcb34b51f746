import numpy as np
import os
import collections
import matplotlib.pyplot as plt
from dm_control import mujoco
from dm_control.rl import control
from dm_control.suite import base

from constants import DT, XML_DIR, QJ2C_JOINT_NAMES, QJ2C_START_ARM_POSE

import IPython
e = IPython.embed

BOX_POSE = [None] # to be changed from outside

def make_qj2c_sim_env(task_name):
    """
    Environment for QJ2C dual-arm robot manipulation.
    Action space:      [right_arm_joints (7),    # 右臂7个关节
                        left_arm_joints (7)]     # 左臂7个关节
    Observation space: [qpos (14), qvel (14), images]
    """
    if 'qj2c_transfer_cube' in task_name:
        xml_path = os.path.join(XML_DIR, 'qj2c', 'qj2c_simple_scene.xml')
        physics = mujoco.Physics.from_xml_path(xml_path)
        task = QJ2CTransferCubeTask(random=False)
        env = control.Environment(physics, task, time_limit=20, control_timestep=DT,
                                  n_sub_steps=None, flat_observation=False)
    elif 'qj2c_bimanual' in task_name:
        xml_path = os.path.join(XML_DIR, 'qj2c', 'qj2c_simple_scene.xml')
        physics = mujoco.Physics.from_xml_path(xml_path)
        task = QJ2CBimanualTask(random=False)
        env = control.Environment(physics, task, time_limit=20, control_timestep=DT,
                                  n_sub_steps=None, flat_observation=False)
    else:
        raise NotImplementedError(f"Task {task_name} not implemented for QJ2C")
    return env

class QJ2CBaseTask(base.Task):
    def __init__(self, random=None):
        super().__init__(random=random)

    def before_step(self, action, physics):
        # 确保动作维度正确 (14个关节)
        if len(action) != 14:
            raise ValueError(f"Expected 14 actions for QJ2C, got {len(action)}")
        
        # 设置关节位置
        physics.data.ctrl[:14] = action

    @staticmethod
    def get_qpos(physics):
        """获取关节位置"""
        qpos_raw = physics.data.qpos.copy()
        # QJ2C有14个关节 + 1个自由关节(立方体)
        # 只返回机械臂的14个关节位置
        return qpos_raw[:14]

    @staticmethod
    def get_qvel(physics):
        """获取关节速度"""
        qvel_raw = physics.data.qvel.copy()
        # 只返回机械臂的14个关节速度
        return qvel_raw[:14]

    @staticmethod
    def get_env_state(physics):
        """获取环境状态（如物体位置）"""
        # 获取立方体的位置和姿态
        cube_pos = physics.data.qpos[14:17].copy()  # 立方体位置
        cube_quat = physics.data.qpos[17:21].copy()  # 立方体四元数
        return np.concatenate([cube_pos, cube_quat])

    def get_observation(self, physics):
        obs = collections.OrderedDict()
        obs['qpos'] = self.get_qpos(physics)
        obs['qvel'] = self.get_qvel(physics)
        obs['env_state'] = self.get_env_state(physics)
        obs['images'] = dict()
        obs['images']['top'] = physics.render(height=480, width=640, camera_id='top')
        obs['images']['qj2c_front'] = physics.render(height=480, width=640, camera_id='qj2c_front')
        obs['images']['vis'] = physics.render(height=480, width=640, camera_id='qj2c_side')
        
        # 添加末端执行器位置信息
        obs['right_tool_pos'] = self.get_site_pos(physics, 'right_tool_site')
        obs['left_tool_pos'] = self.get_site_pos(physics, 'left_tool_site')
        
        return obs

    def get_site_pos(self, physics, site_name):
        """获取site的位置"""
        try:
            site_id = physics.model.name2id(site_name, 'site')
            return physics.data.site_xpos[site_id].copy()
        except:
            return np.zeros(3)

    def initialize_robots(self, physics):
        """初始化机械臂位置"""
        # 设置初始关节位置
        physics.data.qpos[:14] = QJ2C_START_ARM_POSE
        physics.forward()

class QJ2CTransferCubeTask(QJ2CBaseTask):
    def __init__(self, random=None):
        super().__init__(random=random)
        self.max_reward = 4

    def initialize_episode(self, physics):
        """Sets the state of the environment at the start of each episode."""
        self.initialize_robots(physics)
        
        # 随机化立方体位置
        if BOX_POSE[0] is not None:
            cube_pose = BOX_POSE[0]
        else:
            # 默认立方体位置
            cube_pose = np.array([0.3, 0.6, 0.85, 1, 0, 0, 0])  # [x,y,z,qw,qx,qy,qz]
        
        # 设置立方体位置 (假设立方体是第15个body，索引14开始)
        physics.data.qpos[14:21] = cube_pose
        physics.forward()

    def get_reward(self, physics):
        # 简单的奖励函数：基于右臂末端执行器到立方体的距离
        right_tool_pos = self.get_site_pos(physics, 'right_tool_site')
        cube_pos = physics.data.qpos[14:17]
        
        distance = np.linalg.norm(right_tool_pos - cube_pos)
        reward = max(0, 1 - distance)  # 距离越近奖励越高
        
        # 如果立方体移动到目标位置，给额外奖励
        target_pos = np.array([-0.3, 0.6, 0.85])
        cube_to_target_dist = np.linalg.norm(cube_pos - target_pos)
        if cube_to_target_dist < 0.1:
            reward += 3
            
        return reward

class QJ2CBimanualTask(QJ2CBaseTask):
    def __init__(self, random=None):
        super().__init__(random=random)
        self.max_reward = 5

    def initialize_episode(self, physics):
        """双臂协作任务初始化"""
        self.initialize_robots(physics)
        
        # 设置立方体在两臂中间
        cube_pose = np.array([0.0, 0.6, 0.9, 1, 0, 0, 0])
        physics.data.qpos[14:21] = cube_pose
        physics.forward()

    def get_reward(self, physics):
        # 双臂协作奖励：两个末端执行器都要接近立方体
        right_tool_pos = self.get_site_pos(physics, 'right_tool_site')
        left_tool_pos = self.get_site_pos(physics, 'left_tool_site')
        cube_pos = physics.data.qpos[14:17]
        
        right_dist = np.linalg.norm(right_tool_pos - cube_pos)
        left_dist = np.linalg.norm(left_tool_pos - cube_pos)
        
        # 两臂都接近立方体时给奖励
        reward = max(0, 2 - right_dist - left_dist)
        
        # 如果两臂形成合适的抓取姿态，给额外奖励
        arm_distance = np.linalg.norm(right_tool_pos - left_tool_pos)
        if 0.1 < arm_distance < 0.3:  # 合适的抓取距离
            reward += 2
            
        return reward

def sample_qj2c_cube_pose():
    """为QJ2C任务随机采样立方体位置"""
    x_range = [0.1, 0.4]
    y_range = [0.4, 0.8]
    z_range = [0.85, 0.85]

    ranges = np.vstack([x_range, y_range, z_range])
    cube_position = np.random.uniform(ranges[:, 0], ranges[:, 1])

    cube_quat = np.array([1, 0, 0, 0])
    return np.concatenate([cube_position, cube_quat])

# 测试函数
def test_qj2c_env():
    """测试QJ2C环境"""
    print("Testing QJ2C environment...")
    
    env = make_qj2c_sim_env('qj2c_transfer_cube')
    ts = env.reset()
    
    print(f"Observation keys: {ts.observation.keys()}")
    print(f"qpos shape: {ts.observation['qpos'].shape}")
    print(f"qvel shape: {ts.observation['qvel'].shape}")
    print(f"env_state shape: {ts.observation['env_state'].shape}")
    print(f"Images: {list(ts.observation['images'].keys())}")
    
    # 测试随机动作
    for i in range(10):
        action = np.random.uniform(-0.1, 0.1, 14)  # 14个关节的小幅随机动作
        ts = env.step(action)
        print(f"Step {i}: reward = {ts.reward:.3f}")
    
    print("QJ2C environment test completed!")

if __name__ == "__main__":
    test_qj2c_env()
