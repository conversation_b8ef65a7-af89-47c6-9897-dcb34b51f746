#!/usr/bin/env python3
"""
创建机械臂的dependencies.xml文件的脚本
"""

def create_dependencies_xml(robot_name, mesh_files, output_path=None):
    """
    创建dependencies.xml文件
    
    Args:
        robot_name: 机械臂名称
        mesh_files: mesh文件列表，格式为 [{'name': 'mesh_name', 'file': 'file.stl'}, ...]
        output_path: 输出路径，默认为 assets/{robot_name}_dependencies.xml
    """
    
    if output_path is None:
        output_path = f"assets/{robot_name}_dependencies.xml"
    
    content = '''<mujocoinclude>
    <compiler angle="radian" inertiafromgeom="auto" inertiagrouprange="4 5"/>
    <asset>
'''
    
    for mesh in mesh_files:
        # 默认缩放为0.001（从mm转换为m）
        scale = mesh.get('scale', '0.001 0.001 0.001')
        content += f'        <mesh name="{mesh["name"]}" file="{mesh["file"]}" scale="{scale}" />\n'
    
    content += '''    </asset>
</mujocoinclude>'''
    
    with open(output_path, 'w') as f:
        f.write(content)
    
    print(f"Dependencies file created: {output_path}")
    return output_path

# 示例用法
if __name__ == "__main__":
    # QJ2C双臂机械臂的dependencies文件
    robot_name = "qj2c"

    # 基于解析的URDF文件生成的mesh信息
    mesh_files = [
        {'name': 'base_link', 'file': 'qj2c/meshes/base_link.STL', 'scale': '0.001 0.001 0.001'},
        {'name': 'AR1', 'file': 'qj2c/meshes/AR1.STL', 'scale': '0.001 0.001 0.001'},
        {'name': 'AR2', 'file': 'qj2c/meshes/AR2.STL', 'scale': '0.001 0.001 0.001'},
        {'name': 'AR3', 'file': 'qj2c/meshes/AR3.STL', 'scale': '0.001 0.001 0.001'},
        {'name': 'AR4', 'file': 'qj2c/meshes/AR4.STL', 'scale': '0.001 0.001 0.001'},
        {'name': 'AR5', 'file': 'qj2c/meshes/AR5.STL', 'scale': '0.001 0.001 0.001'},
        {'name': 'AR6', 'file': 'qj2c/meshes/AR6.STL', 'scale': '0.001 0.001 0.001'},
        {'name': 'AR7', 'file': 'qj2c/meshes/AR7.STL', 'scale': '0.001 0.001 0.001'},
        {'name': 'AL1', 'file': 'qj2c/meshes/AL1.STL', 'scale': '0.001 0.001 0.001'},
        {'name': 'AL2', 'file': 'qj2c/meshes/AL2.STL', 'scale': '0.001 0.001 0.001'},
        {'name': 'AL3', 'file': 'qj2c/meshes/AL3.STL', 'scale': '0.001 0.001 0.001'},
        {'name': 'AL4', 'file': 'qj2c/meshes/AL4.STL', 'scale': '0.001 0.001 0.001'},
        {'name': 'AL5', 'file': 'qj2c/meshes/AL5.STL', 'scale': '0.001 0.001 0.001'},
        {'name': 'AL6', 'file': 'qj2c/meshes/AL6.STL', 'scale': '0.001 0.001 0.001'},
        {'name': 'AL7', 'file': 'qj2c/meshes/AL7.STL', 'scale': '0.001 0.001 0.001'},
    ]

    # 在qj2c目录下创建dependencies文件
    output_path = "assets/qj2c/qj2c_dependencies.xml"
    create_dependencies_xml(robot_name, mesh_files, output_path)

    print(f"""
QJ2C双臂机械臂dependencies文件已创建！
- 文件位置: {output_path}
- 包含15个mesh文件
- 使用相对路径引用mesh文件
""")
