#!/usr/bin/env python3
"""
创建机械臂的dependencies.xml文件的脚本
"""

def create_dependencies_xml(robot_name, mesh_files, output_path=None):
    """
    创建dependencies.xml文件
    
    Args:
        robot_name: 机械臂名称
        mesh_files: mesh文件列表，格式为 [{'name': 'mesh_name', 'file': 'file.stl'}, ...]
        output_path: 输出路径，默认为 assets/{robot_name}_dependencies.xml
    """
    
    if output_path is None:
        output_path = f"assets/{robot_name}_dependencies.xml"
    
    content = '''<mujocoinclude>
    <compiler angle="radian" inertiafromgeom="auto" inertiagrouprange="4 5"/>
    <asset>
'''
    
    for mesh in mesh_files:
        # 默认缩放为0.001（从mm转换为m）
        scale = mesh.get('scale', '0.001 0.001 0.001')
        content += f'        <mesh name="{mesh["name"]}" file="{mesh["file"]}" scale="{scale}" />\n'
    
    content += '''    </asset>
</mujocoinclude>'''
    
    with open(output_path, 'w') as f:
        f.write(content)
    
    print(f"Dependencies file created: {output_path}")
    return output_path

# 示例用法
if __name__ == "__main__":
    # 示例：创建一个6DOF机械臂的dependencies文件
    robot_name = "your_robot"
    
    # 根据你的URDF文件中的mesh信息填写
    mesh_files = [
        {'name': 'base_link', 'file': 'base_link.stl'},
        {'name': 'link1', 'file': 'link1.stl'},
        {'name': 'link2', 'file': 'link2.stl'},
        {'name': 'link3', 'file': 'link3.stl'},
        {'name': 'link4', 'file': 'link4.stl'},
        {'name': 'link5', 'file': 'link5.stl'},
        {'name': 'link6', 'file': 'link6.stl'},
        {'name': 'gripper_base', 'file': 'gripper_base.stl'},
        {'name': 'gripper_finger1', 'file': 'gripper_finger1.stl'},
        {'name': 'gripper_finger2', 'file': 'gripper_finger2.stl'},
    ]
    
    create_dependencies_xml(robot_name, mesh_files)
    
    print("""
请根据你的URDF文件修改mesh_files列表：
1. 查看URDF中的<mesh>标签
2. 提取filename属性
3. 为每个mesh文件创建对应的条目
4. 确保STL文件已复制到assets目录
""")
