#!/usr/bin/env python3
"""
启动QJ2C机械臂的MuJoCo交互式图形界面
可以看到机械臂、相机位置标记和实时动画
"""

import numpy as np
import mujoco
import mujoco.viewer
import time
import os
from threading import Thread
import signal
import sys

class QJ2CInteractiveViewer:
    def __init__(self, xml_path=None):
        if xml_path is None:
            xml_path = os.path.join('assets', 'qj2c', 'qj2_move.xml')
        
        print(f"🚀 启动QJ2C机械臂交互式查看器")
        print(f"📁 加载XML文件: {xml_path}")
        
        try:
            self.model = mujoco.MjModel.from_xml_path(xml_path)
            self.data = mujoco.MjData(self.model)
            print(f"✅ 模型加载成功!")
        except Exception as e:
            print(f"❌ 模型加载失败: {e}")
            sys.exit(1)
        
        # 动画控制
        self.animation_enabled = True
        self.animation_speed = 1.0
        
        # 初始化机械臂
        self.reset_robot_pose()
        
        print(f"📊 模型信息:")
        print(f"   - 关节数: {self.model.njnt}")
        print(f"   - 执行器数: {self.model.nu}")
        print(f"   - 相机数: {self.model.ncam}")
        print(f"   - 几何体数: {self.model.ngeom}")
        
        self.print_camera_info()
        
    def reset_robot_pose(self):
        """重置机械臂到展示姿态"""
        if self.model.nu >= 14:
            # 右臂展示姿态
            self.data.ctrl[0] = 0.6    # r_arm_Joint1
            self.data.ctrl[1] = -0.4   # r_arm_Joint2  
            self.data.ctrl[2] = 1.0    # r_arm_Joint3
            self.data.ctrl[3] = -0.6   # r_arm_Joint4
            self.data.ctrl[4] = 0.4    # r_arm_Joint5
            self.data.ctrl[5] = 0.3    # r_arm_Joint6
            self.data.ctrl[6] = 0.0    # r_arm_Joint7
            
            # 左臂展示姿态（镜像）
            self.data.ctrl[7] = -0.6   # l_arm_Joint1
            self.data.ctrl[8] = -0.4   # l_arm_Joint2
            self.data.ctrl[9] = -1.0   # l_arm_Joint3
            self.data.ctrl[10] = -0.6  # l_arm_Joint4
            self.data.ctrl[11] = -0.4  # l_arm_Joint5
            self.data.ctrl[12] = -0.3  # l_arm_Joint6
            self.data.ctrl[13] = 0.0   # l_arm_Joint7
        
        mujoco.mj_forward(self.model, self.data)
        
    def print_camera_info(self):
        """打印相机信息"""
        print(f"\n📷 相机配置:")
        main_cameras = ['top', 'qj2c_front', 'qj2c_side']
        
        for i in range(self.model.ncam):
            cam_name = mujoco.mj_id2name(self.model, mujoco.mjtObj.mjOBJ_CAMERA, i)
            if cam_name:
                cam_pos = self.model.cam_pos[i]
                fovy = self.model.cam_fovy[i]
                marker = "🎯" if cam_name in main_cameras else "📹"
                print(f"   {marker} {cam_name}: 位置[{cam_pos[0]:.2f}, {cam_pos[1]:.2f}, {cam_pos[2]:.2f}], 视野{fovy:.0f}°")
        
        print(f"\n🎯 主要训练相机: {', '.join(main_cameras)}")
        print(f"📹 辅助相机: 其他相机用于不同视角观察")
        
    def animate_robot(self):
        """机械臂动画循环"""
        t = 0
        base_pose = self.data.ctrl.copy()
        
        while self.animation_enabled:
            if self.model.nu >= 14:
                # 生成平滑的动画
                amplitude = 0.3 * self.animation_speed
                freq1 = 0.3 * self.animation_speed
                freq2 = 0.5 * self.animation_speed
                
                # 右臂动画
                self.data.ctrl[0] = base_pose[0] + amplitude * np.sin(freq1 * t)
                self.data.ctrl[2] = base_pose[2] + amplitude * np.sin(freq1 * t + np.pi/3)
                self.data.ctrl[4] = base_pose[4] + amplitude * np.sin(freq2 * t + np.pi/2)
                self.data.ctrl[6] = amplitude * 0.5 * np.sin(freq2 * t * 2)
                
                # 左臂动画（相位差）
                self.data.ctrl[7] = base_pose[7] - amplitude * np.sin(freq1 * t + np.pi)
                self.data.ctrl[9] = base_pose[9] - amplitude * np.sin(freq1 * t + np.pi + np.pi/3)
                self.data.ctrl[11] = base_pose[11] - amplitude * np.sin(freq2 * t + np.pi + np.pi/2)
                self.data.ctrl[13] = -amplitude * 0.5 * np.sin(freq2 * t * 2 + np.pi)
            
            t += 0.02
            time.sleep(0.02)
    
    def print_controls(self):
        """打印控制说明"""
        print(f"\n🎮 交互式控制说明:")
        print(f"   🖱️  鼠标左键拖拽: 旋转视角")
        print(f"   🖱️  鼠标右键拖拽: 平移视角")
        print(f"   🖱️  鼠标滚轮: 缩放视角")
        print(f"   ⌨️  空格键: 暂停/继续仿真")
        print(f"   ⌨️  Ctrl+R: 重置仿真")
        print(f"   ⌨️  Tab: 切换相机视角")
        print(f"   ⌨️  F1: 显示帮助")
        print(f"   ❌  ESC或关闭窗口: 退出程序")
        print()
        print(f"🔍 观察要点:")
        print(f"   🔴 红色球体: TOP相机位置 (俯视角)")
        print(f"   🟢 绿色球体: FRONT相机位置 (前视角)")
        print(f"   🔵 蓝色球体: SIDE相机位置 (侧视角)")
        print(f"   🤖 双臂机械臂: 自动执行展示动作")
        print(f"   📦 红色立方体: 测试物体")
        print(f"   🟩 绿色方块: 目标位置")
        print()
    
    def setup_viewer_camera(self, viewer):
        """设置查看器的初始相机位置"""
        # 设置一个好的初始视角来观察整个场景
        viewer.cam.azimuth = 135      # 方位角
        viewer.cam.elevation = -25    # 仰角
        viewer.cam.distance = 3.0     # 距离
        viewer.cam.lookat = [0, 0.6, 0.9]  # 注视点（机械臂基座附近）
        
    def run(self):
        """运行交互式查看器"""
        self.print_controls()
        
        # 启动动画线程
        animation_thread = Thread(target=self.animate_robot, daemon=True)
        animation_thread.start()
        
        print(f"🎬 启动图形界面...")
        print(f"   (如果窗口没有出现，请检查显示设置)")
        
        try:
            with mujoco.viewer.launch_passive(self.model, self.data) as viewer:
                # 设置初始相机位置
                self.setup_viewer_camera(viewer)
                
                print(f"✅ 图形界面已启动!")
                print(f"   窗口标题: MuJoCo")
                print(f"   可以开始交互了...")
                
                # 主循环
                step_count = 0
                while viewer.is_running():
                    step_start = time.time()
                    
                    # 仿真步进
                    mujoco.mj_step(self.model, self.data)
                    step_count += 1
                    
                    # 每1000步打印一次状态
                    if step_count % 1000 == 0:
                        print(f"⏱️  仿真步数: {step_count}, 时间: {step_count * self.model.opt.timestep:.1f}s")
                    
                    # 同步到实时
                    time_until_next_step = self.model.opt.timestep - (time.time() - step_start)
                    if time_until_next_step > 0:
                        time.sleep(time_until_next_step)
                        
        except KeyboardInterrupt:
            print(f"\n⚠️  收到中断信号，正在退出...")
        except Exception as e:
            print(f"\n❌ 运行时错误: {e}")
        finally:
            self.animation_enabled = False
            print(f"👋 QJ2C查看器已关闭")

def signal_handler(sig, frame):
    """处理Ctrl+C信号"""
    print(f"\n⚠️  收到退出信号，正在关闭...")
    sys.exit(0)

def main():
    # 设置信号处理
    signal.signal(signal.SIGINT, signal_handler)
    
    print(f"=" * 60)
    print(f"🤖 QJ2C双臂机械臂 - MuJoCo交互式查看器")
    print(f"=" * 60)
    
    try:
        viewer = QJ2CInteractiveViewer()
        viewer.run()
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print(f"\n🔧 故障排除:")
        print(f"   1. 确保已激活conda环境: conda activate env_RSB")
        print(f"   2. 检查XML文件是否存在")
        print(f"   3. 确保有图形显示支持")
        print(f"   4. 尝试运行: python3 demo_qj2c_cameras.py")

if __name__ == "__main__":
    main()
