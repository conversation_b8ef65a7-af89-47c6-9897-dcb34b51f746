#!/usr/bin/env python3
"""
QJ2C机械臂相机视图演示
展示三个主要相机的视角
"""

import numpy as np
import mujoco
import os
import matplotlib.pyplot as plt

def demo_qj2c_cameras():
    """演示QJ2C机械臂的相机视图"""
    
    # 加载模型
    xml_path = os.path.join('assets', 'qj2c', 'qj2c_simple_scene.xml')
    print(f"Loading XML: {xml_path}")
    
    try:
        model = mujoco.MjModel.from_xml_path(xml_path)
        data = mujoco.MjData(model)
        print("✓ 模型加载成功!")
    except Exception as e:
        print(f"✗ 模型加载失败: {e}")
        return
    
    # 设置机械臂姿态
    print("设置机械臂姿态...")
    if model.nu >= 14:
        # 右臂姿态
        data.ctrl[0] = 0.5    # r_arm_Joint1
        data.ctrl[1] = -0.3   # r_arm_Joint2  
        data.ctrl[2] = 0.8    # r_arm_Joint3
        data.ctrl[3] = -0.5   # r_arm_Joint4
        data.ctrl[4] = 0.3    # r_arm_Joint5
        data.ctrl[5] = 0.2    # r_arm_Joint6
        data.ctrl[6] = 0.0    # r_arm_Joint7
        
        # 左臂姿态
        data.ctrl[7] = -0.5   # l_arm_Joint1
        data.ctrl[8] = -0.3   # l_arm_Joint2
        data.ctrl[9] = -0.8   # l_arm_Joint3
        data.ctrl[10] = -0.5  # l_arm_Joint4
        data.ctrl[11] = -0.3  # l_arm_Joint5
        data.ctrl[12] = -0.2  # l_arm_Joint6
        data.ctrl[13] = 0.0   # l_arm_Joint7
    
    # 运行仿真步骤让机械臂到达目标位置
    for _ in range(100):
        mujoco.mj_step(model, data)
    
    print("✓ 机械臂姿态设置完成")
    
    # 打印相机信息
    print(f"\n=== 相机信息 ===")
    print(f"总共发现 {model.ncam} 个相机:")
    for i in range(model.ncam):
        cam_name = mujoco.mj_id2name(model, mujoco.mjtObj.mjOBJ_CAMERA, i)
        if cam_name:
            cam_pos = model.cam_pos[i]
            fovy = model.cam_fovy[i]
            print(f"  {i}: {cam_name} - 位置: [{cam_pos[0]:.2f}, {cam_pos[1]:.2f}, {cam_pos[2]:.2f}], 视野: {fovy:.1f}°")
    
    # 捕获主要相机的图像
    target_cameras = ['top', 'qj2c_front', 'qj2c_side']
    camera_images = {}
    
    print(f"\n=== 捕获相机图像 ===")
    
    # 创建渲染器
    renderer = mujoco.Renderer(model, height=480, width=640)
    
    for cam_name in target_cameras:
        try:
            # 查找相机ID
            cam_id = None
            for i in range(model.ncam):
                if mujoco.mj_id2name(model, mujoco.mjtObj.mjOBJ_CAMERA, i) == cam_name:
                    cam_id = i
                    break
            
            if cam_id is not None:
                # 渲染图像
                renderer.update_scene(data, camera=cam_id)
                image = renderer.render()
                camera_images[cam_name] = image
                print(f"✓ {cam_name}: 图像尺寸 {image.shape}")
            else:
                print(f"✗ 未找到相机: {cam_name}")
                
        except Exception as e:
            print(f"✗ 捕获相机 {cam_name} 失败: {e}")
    
    # 显示图像
    if camera_images:
        print(f"\n=== 显示相机视图 ===")
        
        fig, axes = plt.subplots(1, len(camera_images), figsize=(18, 6))
        if len(camera_images) == 1:
            axes = [axes]
        
        colors = ['red', 'green', 'blue']
        for i, (cam_name, image) in enumerate(camera_images.items()):
            axes[i].imshow(image)
            axes[i].set_title(f'{cam_name.upper()} Camera View', 
                            fontsize=14, fontweight='bold', color=colors[i % len(colors)])
            axes[i].axis('off')
            
            # 添加相机位置信息
            cam_id = None
            for j in range(model.ncam):
                if mujoco.mj_id2name(model, mujoco.mjtObj.mjOBJ_CAMERA, j) == cam_name:
                    cam_id = j
                    break
            
            if cam_id is not None:
                cam_pos = model.cam_pos[cam_id]
                fovy = model.cam_fovy[cam_id]
                info_text = f'位置: [{cam_pos[0]:.2f}, {cam_pos[1]:.2f}, {cam_pos[2]:.2f}]\n视野: {fovy:.1f}°'
                axes[i].text(0.02, 0.98, info_text, transform=axes[i].transAxes, 
                           fontsize=10, verticalalignment='top', 
                           bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
        
        plt.suptitle('QJ2C双臂机械臂 - 相机视图演示', fontsize=16, fontweight='bold')
        plt.tight_layout()
        
        # 保存图像
        filename = "qj2c_camera_views_demo.png"
        plt.savefig(filename, dpi=150, bbox_inches='tight')
        print(f"✓ 相机视图已保存: {filename}")
        
        # 显示图像
        plt.show()
        
        # 打印相机视野说明
        print(f"\n=== 相机视野说明 ===")
        print("🔴 TOP Camera (红色标记):")
        print("   - 俯视角度，适合观察整体布局和物体位置")
        print("   - 位置: 机械臂正上方")
        print()
        print("🟢 QJ2C_FRONT Camera (绿色标记):")
        print("   - 前视角度，适合观察机械臂正面动作")
        print("   - 位置: 机械臂前方")
        print()
        print("🔵 QJ2C_SIDE Camera (蓝色标记):")
        print("   - 侧视角度，适合观察机械臂侧面动作")
        print("   - 位置: 机械臂侧方")
        print()
        print("这三个相机提供了机械臂的全方位视角，")
        print("为ACT训练提供丰富的视觉信息。")
        
    else:
        print("✗ 未能捕获任何相机图像")
    
    # renderer对象不需要显式关闭
    print(f"\n=== 演示完成 ===")

def print_environment_info():
    """打印环境信息"""
    print("=== QJ2C机械臂环境信息 ===")
    print("🤖 机械臂配置:")
    print("   - 双臂结构，每臂7个关节")
    print("   - 总共14个可动关节")
    print("   - 使用简化几何体显示")
    print()
    print("📷 相机配置:")
    print("   - 3个主要训练相机: top, qj2c_front, qj2c_side")
    print("   - 4个辅助相机: left_pillar, right_pillar, angle, qj2c_top_close")
    print("   - 相机位置用彩色球体标记")
    print()
    print("🎯 可视化特性:")
    print("   - 相机位置标记 (红/绿/蓝球体)")
    print("   - 相机朝向指示 (圆柱体)")
    print("   - 机械臂动态演示")
    print("   - 多视角图像捕获")
    print()

if __name__ == "__main__":
    print_environment_info()
    demo_qj2c_cameras()
