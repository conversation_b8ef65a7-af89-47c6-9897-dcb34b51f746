<mujoco>
    <!-- QJ2C双臂机械臂完整场景文件 -->
    
    <option timestep='0.002' iterations="50" tolerance="1e-10" solver="Newton" jacobian="dense" cone="elliptic"/>
    
    <!-- 包含依赖文件 -->
    <include file="qj2c_dependencies.xml"/>
    
    <!-- 资源定义 -->
    <asset>
        <!-- 纹理 -->
        <texture name="wood" type="2d" file="../textures/wood.png" width="256" height="256"/>
        <material name="wood_mat" texture="wood" rgba="1 1 1 1"/>
    </asset>
    
    <!-- 视觉设置 -->
    <visual>
        <map fogstart="1.5" fogend="5" force="0.1" znear="0.1"/>
        <quality shadowsize="4096" offsamples="4"/>
        <headlight ambient="0.4 0.4 0.4"/>
    </visual>
    
    <!-- 执行器定义 -->
    <actuator>
        <!-- 右臂执行器 -->
        <position name="r_arm_Joint1_actuator" joint="qj2c/r_arm_Joint1" kp="100"/>
        <position name="r_arm_Joint2_actuator" joint="qj2c/r_arm_Joint2" kp="100"/>
        <position name="r_arm_Joint3_actuator" joint="qj2c/r_arm_Joint3" kp="100"/>
        <position name="r_arm_Joint4_actuator" joint="qj2c/r_arm_Joint4" kp="100"/>
        <position name="r_arm_Joint5_actuator" joint="qj2c/r_arm_Joint5" kp="100"/>
        <position name="r_arm_Joint6_actuator" joint="qj2c/r_arm_Joint6" kp="100"/>
        <position name="r_arm_Joint7_actuator" joint="qj2c/r_arm_Joint7" kp="100"/>
        
        <!-- 左臂执行器 -->
        <position name="l_arm_Joint1_actuator" joint="qj2c/l_arm_Joint1" kp="100"/>
        <position name="l_arm_Joint2_actuator" joint="qj2c/l_arm_Joint2" kp="100"/>
        <position name="l_arm_Joint3_actuator" joint="qj2c/l_arm_Joint3" kp="100"/>
        <position name="l_arm_Joint4_actuator" joint="qj2c/l_arm_Joint4" kp="100"/>
        <position name="l_arm_Joint5_actuator" joint="qj2c/l_arm_Joint5" kp="100"/>
        <position name="l_arm_Joint6_actuator" joint="qj2c/l_arm_Joint6" kp="100"/>
        <position name="l_arm_Joint7_actuator" joint="qj2c/l_arm_Joint7" kp="100"/>
    </actuator>
    
    <!-- 世界体 -->
    <worldbody>
        <!-- 光照 -->
        <light castshadow="false" directional='true' diffuse='.3 .3 .3' specular='0.3 0.3 0.3' pos='-1 -1 1' dir='1 1 -1'/>
        <light directional='true' diffuse='.3 .3 .3' specular='0.3 0.3 0.3' pos='1 -1 1' dir='-1 1 -1'/>
        <light castshadow="false" directional='true' diffuse='.3 .3 .3' specular='0.3 0.3 0.3' pos='0 1 1' dir='0 -1 -1'/>
        
        <!-- 地面 -->
        <geom name="floor" type="plane" size="2 2 0.1" rgba="0.9 0.9 0.9 1" contype="1" conaffinity="1"/>
        
        <!-- 工作台 -->
        <body name="table" pos="0 0.6 0">
            <geom name="table_surface" type="box" size="0.8 0.6 0.05" pos="0 0 0.4" 
                  conaffinity="1" contype="1" rgba="0.8 0.6 0.4 1"/>
            <!-- 桌腿 -->
            <geom name="table_leg1" type="cylinder" size="0.03 0.2" pos="0.6 0.4 0.2" rgba="0.6 0.4 0.2 1"/>
            <geom name="table_leg2" type="cylinder" size="0.03 0.2" pos="-0.6 0.4 0.2" rgba="0.6 0.4 0.2 1"/>
            <geom name="table_leg3" type="cylinder" size="0.03 0.2" pos="0.6 -0.4 0.2" rgba="0.6 0.4 0.2 1"/>
            <geom name="table_leg4" type="cylinder" size="0.03 0.2" pos="-0.6 -0.4 0.2" rgba="0.6 0.4 0.2 1"/>
        </body>
        
        <!-- 测试立方体 -->
        <body name="red_cube" pos="0.3 0.6 0.5">
            <joint name="cube_free_joint" type="free"/>
            <geom name="cube_geom" type="box" size="0.025 0.025 0.025" rgba="1 0 0 1" 
                  contype="1" conaffinity="1" mass="0.1"/>
        </body>
        
        <!-- 目标位置标记 -->
        <body name="target_marker" pos="-0.3 0.6 0.5">
            <geom name="target_geom" type="box" size="0.03 0.03 0.005" rgba="0 1 0 0.5" 
                  contype="0" conaffinity="0"/>
        </body>
        
        <!-- 包含QJ2C机械臂 -->
        <include file="qj2c.xml"/>
        
        <!-- 相机设置 -->
        <camera name="left_pillar" pos="-0.5 0.2 0.6" fovy="78" mode="targetbody" target="table"/>
        <camera name="right_pillar" pos="0.5 0.2 0.6" fovy="78" mode="targetbody" target="table"/>
        <camera name="top" pos="0 0.6 0.8" fovy="78" mode="targetbody" target="table"/>
        <camera name="angle" pos="0 0 0.6" fovy="78" mode="targetbody" target="table"/>
        <camera name="qj2c_front" pos="0.8 0.6 1.2" fovy="45" mode="targetbody" target="table"/>
        <camera name="qj2c_side" pos="0 1.4 1.2" fovy="45" mode="targetbody" target="table"/>
        <camera name="qj2c_top_close" pos="0 0.6 1.5" fovy="60" mode="targetbody" target="table"/>
        
        <!-- 相机位置可视化标记 -->
        <body name="camera_markers">
            <!-- Top相机标记 -->
            <body name="top_camera_marker" pos="0 0.6 0.8">
                <geom type="sphere" size="0.02" rgba="1 0 0 0.8" name="top_cam_marker" contype="0" conaffinity="0"/>
                <geom type="cylinder" size="0.01 0.05" rgba="1 0 0 0.6" name="top_cam_direction" 
                      pos="0 0 -0.05" contype="0" conaffinity="0"/>
                <site name="top_cam_site" pos="0 0 0" size="0.01" rgba="1 0 0 1"/>
            </body>
            
            <!-- QJ2C Front相机标记 -->
            <body name="qj2c_front_camera_marker" pos="0.8 0.6 1.2">
                <geom type="sphere" size="0.02" rgba="0 1 0 0.8" name="front_cam_marker" contype="0" conaffinity="0"/>
                <geom type="cylinder" size="0.01 0.05" rgba="0 1 0 0.6" name="front_cam_direction" 
                      pos="-0.05 0 0" euler="0 1.57 0" contype="0" conaffinity="0"/>
                <site name="front_cam_site" pos="0 0 0" size="0.01" rgba="0 1 0 1"/>
            </body>
            
            <!-- QJ2C Side相机标记 -->
            <body name="qj2c_side_camera_marker" pos="0 1.4 1.2">
                <geom type="sphere" size="0.02" rgba="0 0 1 0.8" name="side_cam_marker" contype="0" conaffinity="0"/>
                <geom type="cylinder" size="0.01 0.05" rgba="0 0 1 0.6" name="side_cam_direction" 
                      pos="0 -0.05 0" euler="1.57 0 0" contype="0" conaffinity="0"/>
                <site name="side_cam_site" pos="0 0 0" size="0.01" rgba="0 0 1 1"/>
            </body>
        </body>
        
        <!-- 辅助标记点 -->
        <body name="midair" pos="0 0.6 0.2">
            <site pos="0 0 0" size="0.01" type="sphere" name="midair" rgba="1 0 0 0"/>
        </body>
    </worldbody>
</mujoco>
