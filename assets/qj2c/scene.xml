<mujocoinclude>
<!--    <option timestep='0.0025' iterations="50" tolerance="1e-10" solver="Newton" jacobian="dense" cone="elliptic"/>-->

    <asset>
        <mesh file="../tabletop.stl" name="tabletop" scale="0.001 0.001 0.001"/>
    </asset>

    <visual>
        <map fogstart="1.5" fogend="5" force="0.1" znear="0.1"/>
        <quality shadowsize="4096" offsamples="4"/>
        <headlight ambient="0.4 0.4 0.4"/>
    </visual>

    <worldbody>
        <light castshadow="false" directional='true' diffuse='.3 .3 .3' specular='0.3 0.3 0.3' pos='-1 -1 1'
               dir='1 1 -1'/>
        <light directional='true' diffuse='.3 .3 .3' specular='0.3 0.3 0.3' pos='1 -1 1' dir='-1 1 -1'/>
        <light castshadow="false" directional='true' diffuse='.3 .3 .3' specular='0.3 0.3 0.3' pos='0 1 1'
               dir='0 -1 -1'/>

        <body name="table" pos="0 .6 0">
            <geom group="1" mesh="tabletop" pos="0 0 0" type="mesh" conaffinity="1" contype="1" name="table" rgba="0.2 0.2 0.2 1" />
        </body>
        <body name="midair" pos="0 .6 0.2">
            <site pos="0 0 0" size="0.01" type="sphere" name="midair" rgba="1 0 0 0"/>
        </body>

        <camera name="left_pillar" pos="-0.5 0.2 0.6" fovy="78" mode="targetbody" target="table"/>
        <camera name="right_pillar" pos="0.5 0.2 0.6" fovy="78" mode="targetbody" target="table"/>
        <camera name="top" pos="0 0.6 0.8" fovy="78" mode="targetbody" target="table"/>
        <camera name="angle" pos="0 0 0.6" fovy="78" mode="targetbody" target="table"/>
        <camera name="front_close" pos="0 0.2 0.4" fovy="78" mode="targetbody" target="qj2c_base"/>
        <!-- <camera name="qj2c_top_close" pos="0 0.6 1.5" fovy="60" mode="targetbody" target="qj2c_base"/> -->
    </worldbody>



</mujocoinclude>