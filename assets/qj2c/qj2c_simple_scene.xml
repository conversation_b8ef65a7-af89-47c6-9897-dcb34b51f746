<mujoco>
    <!-- QJ2C双臂机械臂简化场景文件 -->
    
    <!-- 场景配置 -->
    <asset>
        <mesh file="../tabletop.stl" name="tabletop" scale="0.001 0.001 0.001"/>
    </asset>

    <visual>
        <map fogstart="1.5" fogend="5" force="0.1" znear="0.1"/>
        <quality shadowsize="4096" offsamples="4"/>
        <headlight ambient="0.4 0.4 0.4"/>
    </visual>

    <!-- 执行器定义 -->
    <actuator>
        <!-- 右臂执行器 -->
        <position name="r_arm_Joint1_actuator" joint="qj2c/r_arm_Joint1" kp="100"/>
        <position name="r_arm_Joint2_actuator" joint="qj2c/r_arm_Joint2" kp="100"/>
        <position name="r_arm_Joint3_actuator" joint="qj2c/r_arm_Joint3" kp="100"/>
        <position name="r_arm_Joint4_actuator" joint="qj2c/r_arm_Joint4" kp="100"/>
        <position name="r_arm_Joint5_actuator" joint="qj2c/r_arm_Joint5" kp="100"/>
        <position name="r_arm_Joint6_actuator" joint="qj2c/r_arm_Joint6" kp="100"/>
        <position name="r_arm_Joint7_actuator" joint="qj2c/r_arm_Joint7" kp="100"/>

        <!-- 左臂执行器 -->
        <position name="l_arm_Joint1_actuator" joint="qj2c/l_arm_Joint1" kp="100"/>
        <position name="l_arm_Joint2_actuator" joint="qj2c/l_arm_Joint2" kp="100"/>
        <position name="l_arm_Joint3_actuator" joint="qj2c/l_arm_Joint3" kp="100"/>
        <position name="l_arm_Joint4_actuator" joint="qj2c/l_arm_Joint4" kp="100"/>
        <position name="l_arm_Joint5_actuator" joint="qj2c/l_arm_Joint5" kp="100"/>
        <position name="l_arm_Joint6_actuator" joint="qj2c/l_arm_Joint6" kp="100"/>
        <position name="l_arm_Joint7_actuator" joint="qj2c/l_arm_Joint7" kp="100"/>
    </actuator>
    
    <worldbody>
        <!-- 场景环境 -->
        <light castshadow="false" directional='true' diffuse='.3 .3 .3' specular='0.3 0.3 0.3' pos='-1 -1 1' dir='1 1 -1'/>
        <light directional='true' diffuse='.3 .3 .3' specular='0.3 0.3 0.3' pos='1 -1 1' dir='-1 1 -1'/>
        <light castshadow="false" directional='true' diffuse='.3 .3 .3' specular='0.3 0.3 0.3' pos='0 1 1' dir='0 -1 -1'/>

        <body name="table" pos="0 .6 0">
            <geom group="1" mesh="tabletop" pos="0 0 0" type="mesh" conaffinity="1" contype="1" name="table" rgba="0.2 0.2 0.2 1" />
        </body>
        <body name="midair" pos="0 .6 0.2">
            <site pos="0 0 0" size="0.01" type="sphere" name="midair" rgba="1 0 0 0"/>
        </body>

        <!-- 相机设置 -->
        <camera name="left_pillar" pos="-0.5 0.2 0.6" fovy="78" mode="targetbody" target="table"/>
        <camera name="right_pillar" pos="0.5 0.2 0.6" fovy="78" mode="targetbody" target="table"/>
        <camera name="top" pos="0 0.6 0.8" fovy="78" mode="targetbody" target="table"/>
        <camera name="angle" pos="0 0 0.6" fovy="78" mode="targetbody" target="table"/>
        
        <!-- 包含QJ2C机械臂简化版本 -->
        <include file="qj2c_simple.xml"/>
        
        <!-- 测试物体 - 立方体 -->
        <body name="test_cube" pos="0.3 0.6 0.85">
            <joint name="cube_joint" type="free" frictionloss="0.01" />
            <inertial pos="0 0 0" mass="0.05" diaginertia="0.002 0.002 0.002" />
            <geom condim="4" solimp="2 1 0.01" solref="0.01 1" friction="1 0.005 0.0001" 
                  pos="0 0 0" size="0.02 0.02 0.02" type="box" name="test_cube" rgba="1 0 0 1" />
        </body>
        
        <!-- 目标位置标记 -->
        <body name="target_position" pos="-0.3 0.6 0.85">
            <geom pos="0 0 0" size="0.025 0.025 0.025" type="box" name="target" 
                  rgba="0 1 0 0.5" contype="0" conaffinity="0" />
        </body>
        
        <!-- 额外的相机视角 -->
        <camera name="qj2c_front" pos="0.8 0.6 1.2" fovy="45" mode="targetbody" target="qj2c_base"/>
        <camera name="qj2c_side" pos="0 1.4 1.2" fovy="45" mode="targetbody" target="qj2c_base"/>
        <camera name="qj2c_top_close" pos="0 0.6 1.5" fovy="60" mode="targetbody" target="qj2c_base"/>
    </worldbody>
</mujoco>
