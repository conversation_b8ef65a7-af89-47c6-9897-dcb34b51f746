<mujocoinclude>
    <compiler angle="radian" inertiafromgeom="auto" inertiagrouprange="4 5"/>
    <asset>
        <mesh name="base_link" file="meshes/base_link.STL" scale="0.001 0.001 0.001" />
        <mesh name="AR1" file="meshes/AR1.STL" scale="0.001 0.001 0.001" />
        <mesh name="AR2" file="meshes/AR2.STL" scale="0.001 0.001 0.001" />
        <mesh name="AR3" file="meshes/AR3.STL" scale="0.001 0.001 0.001" />
        <mesh name="AR4" file="meshes/AR4.STL" scale="0.001 0.001 0.001" />
        <mesh name="AR5" file="meshes/AR5.STL" scale="0.001 0.001 0.001" />
        <mesh name="AR6" file="meshes/AR6.STL" scale="0.001 0.001 0.001" />
        <mesh name="AR7" file="meshes/AR7.STL" scale="0.001 0.001 0.001" />
        <mesh name="AL1" file="meshes/AL1.STL" scale="0.001 0.001 0.001" />
        <mesh name="AL2" file="meshes/AL2.STL" scale="0.001 0.001 0.001" />
        <mesh name="AL3" file="meshes/AL3.STL" scale="0.001 0.001 0.001" />
        <mesh name="AL4" file="meshes/AL4.STL" scale="0.001 0.001 0.001" />
        <mesh name="AL5" file="meshes/AL5.STL" scale="0.001 0.001 0.001" />
        <mesh name="AL6" file="meshes/AL6.STL" scale="0.001 0.001 0.001" />
        <mesh name="AL7" file="meshes/AL7.STL" scale="0.001 0.001 0.001" />
    </asset>
</mujocoinclude>