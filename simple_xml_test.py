#!/usr/bin/env python3
"""
简单的XML文件语法检查
"""

import xml.etree.ElementTree as ET
import os

def test_xml_syntax():
    """测试XML文件语法"""
    
    files_to_test = [
        'assets/qj2c/qj2c_dependencies.xml',
        'assets/qj2c/qj2c.xml',
        'assets/qj2c/qj2c_scene.xml'
    ]
    
    for xml_file in files_to_test:
        print(f"\n测试文件: {xml_file}")
        
        if not os.path.exists(xml_file):
            print(f"✗ 文件不存在")
            continue
            
        try:
            tree = ET.parse(xml_file)
            root = tree.getroot()
            print(f"✓ XML语法正确")
            print(f"  根元素: {root.tag}")
            print(f"  子元素数量: {len(root)}")
            
            # 检查mesh引用
            if 'dependencies' in xml_file:
                meshes = root.findall('.//mesh')
                print(f"  定义的mesh数量: {len(meshes)}")
                for mesh in meshes[:5]:  # 只显示前5个
                    name = mesh.get('name')
                    file = mesh.get('file')
                    print(f"    {name} -> {file}")
                    
                    # 检查文件是否存在
                    mesh_path = os.path.join('assets', file)
                    if os.path.exists(mesh_path):
                        print(f"      ✓ mesh文件存在")
                    else:
                        print(f"      ✗ mesh文件不存在: {mesh_path}")
            
            # 检查关节定义
            if xml_file.endswith('qj2c.xml'):
                joints = root.findall('.//joint')
                print(f"  定义的关节数量: {len(joints)}")
                for joint in joints[:5]:  # 只显示前5个
                    name = joint.get('name')
                    axis = joint.get('axis')
                    range_val = joint.get('range')
                    print(f"    {name}: axis={axis}, range={range_val}")
                    
        except ET.ParseError as e:
            print(f"✗ XML语法错误: {e}")
        except Exception as e:
            print(f"✗ 其他错误: {e}")

def check_mesh_files():
    """检查mesh文件是否存在"""
    print(f"\n=== 检查mesh文件 ===")
    
    mesh_dir = 'assets/qj2c/meshes'
    if not os.path.exists(mesh_dir):
        print(f"✗ mesh目录不存在: {mesh_dir}")
        return
    
    mesh_files = [f for f in os.listdir(mesh_dir) if f.endswith('.STL')]
    print(f"找到 {len(mesh_files)} 个STL文件:")
    
    for mesh_file in sorted(mesh_files):
        file_path = os.path.join(mesh_dir, mesh_file)
        file_size = os.path.getsize(file_path)
        print(f"  {mesh_file}: {file_size/1024:.1f} KB")

def generate_constants_config():
    """生成constants.py的配置建议"""
    print(f"\n=== Constants.py配置建议 ===")
    
    # QJ2C双臂机械臂的关节名称
    joint_names = [
        "r_arm_Joint1", "r_arm_Joint2", "r_arm_Joint3", "r_arm_Joint4", 
        "r_arm_Joint5", "r_arm_Joint6", "r_arm_Joint7",
        "l_arm_Joint1", "l_arm_Joint2", "l_arm_Joint3", "l_arm_Joint4", 
        "l_arm_Joint5", "l_arm_Joint6", "l_arm_Joint7"
    ]
    
    print(f"# QJ2C双臂机械臂配置")
    print(f"JOINT_NAMES = {joint_names}")
    print(f"")
    print(f"# 初始关节位置 (14个关节，全部设为0)")
    print(f"START_ARM_POSE = [0.0] * 14")
    print(f"")
    print(f"# 状态维度 (14个关节)")
    print(f"STATE_DIM = 14")
    print(f"")
    print(f"# 任务配置")
    print(f"SIM_TASK_CONFIGS = {{")
    print(f"    'qj2c_transfer_cube': {{")
    print(f"        'dataset_dir': 'data/qj2c_transfer_cube',")
    print(f"        'num_episodes': 50,")
    print(f"        'episode_len': 400,")
    print(f"        'camera_names': ['top', 'qj2c_front']")
    print(f"    }}")
    print(f"}}")

if __name__ == "__main__":
    print("=== QJ2C XML文件检查 ===")
    
    test_xml_syntax()
    check_mesh_files()
    generate_constants_config()
    
    print(f"\n=== 总结 ===")
    print(f"1. 检查XML文件语法和结构")
    print(f"2. 验证mesh文件存在性")
    print(f"3. 生成constants.py配置建议")
    print(f"")
    print(f"如果所有检查都通过，可以继续下一步：")
    print(f"1. 修改constants.py")
    print(f"2. 修改sim_env.py")
    print(f"3. 测试仿真环境")
