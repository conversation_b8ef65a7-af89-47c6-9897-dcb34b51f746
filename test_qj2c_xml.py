#!/usr/bin/env python3
"""
测试QJ2C机械臂XML文件是否能正确加载
"""

import os
import numpy as np
from dm_control import mujoco
from dm_control.rl import control
from dm_control.suite import base
import matplotlib.pyplot as plt

def test_qj2c_xml():
    """测试QJ2C XML文件加载"""

    # 先测试简化版本
    xml_path = os.path.join('assets', 'qj2c', 'qj2c_simple_scene.xml')
    
    if not os.path.exists(xml_path):
        print(f"XML文件不存在: {xml_path}")
        return False
    
    try:
        print(f"正在加载XML文件: {xml_path}")
        physics = mujoco.Physics.from_xml_path(xml_path)
        print("✓ XML文件加载成功！")
        
        # 检查模型信息
        print(f"\n模型信息:")
        print(f"- 关节数量: {physics.model.njnt}")
        print(f"- 自由度: {physics.model.nq}")
        print(f"- 执行器数量: {physics.model.nu}")
        print(f"- 几何体数量: {physics.model.ngeom}")
        print(f"- 网格数量: {physics.model.nmesh}")
        
        # 打印关节名称
        print(f"\n关节名称:")
        for i in range(physics.model.njnt):
            joint_name = physics.model.joint(i).name
            if joint_name:  # 跳过空名称
                print(f"  {i}: {joint_name}")
        
        # 打印几何体名称
        print(f"\n几何体名称 (前10个):")
        for i in range(min(10, physics.model.ngeom)):
            geom_name = physics.model.geom(i).name
            if geom_name:
                print(f"  {i}: {geom_name}")
        
        # 测试仿真步进
        print(f"\n测试仿真步进...")
        for i in range(10):
            physics.step()
        print("✓ 仿真步进正常！")
        
        # 测试渲染
        print(f"\n测试渲染...")
        try:
            image = physics.render(height=240, width=320, camera_id='top')
            print(f"✓ 渲染成功！图像尺寸: {image.shape}")
            
            # 保存测试图像
            plt.figure(figsize=(8, 6))
            plt.imshow(image)
            plt.title("QJ2C机械臂测试渲染")
            plt.axis('off')
            plt.savefig('qj2c_test_render.png', dpi=150, bbox_inches='tight')
            print("✓ 测试图像已保存: qj2c_test_render.png")
            
        except Exception as e:
            print(f"✗ 渲染失败: {e}")
        
        # 测试关节控制
        print(f"\n测试关节控制...")
        try:
            # 获取当前关节位置
            qpos = physics.data.qpos.copy()
            print(f"当前关节位置数量: {len(qpos)}")
            
            # 设置一个简单的关节位置
            if len(qpos) >= 14:  # 确保有足够的关节
                # 设置右臂第一个关节
                qpos[0] = 0.5  # 假设第一个关节是右臂Joint1
                physics.data.qpos[:] = qpos
                physics.forward()
                print("✓ 关节控制测试成功！")
            else:
                print(f"关节数量不足，期望至少14个，实际{len(qpos)}个")
                
        except Exception as e:
            print(f"✗ 关节控制测试失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"✗ XML文件加载失败: {e}")
        print(f"错误详情: {str(e)}")
        return False

def analyze_joint_structure(physics):
    """分析关节结构"""
    print(f"\n=== 关节结构分析 ===")
    
    # 分析关节类型和范围
    for i in range(physics.model.njnt):
        joint_name = physics.model.joint(i).name
        if joint_name and 'arm_Joint' in joint_name:
            joint_type = physics.model.jnt_type[i]
            joint_range = physics.model.jnt_range[i]
            print(f"{joint_name}: 类型={joint_type}, 范围={joint_range}")

if __name__ == "__main__":
    print("=== QJ2C机械臂XML文件测试 ===")
    
    success = test_qj2c_xml()
    
    if success:
        print(f"\n🎉 所有测试通过！QJ2C机械臂XML文件配置正确。")
        print(f"\n下一步可以:")
        print(f"1. 修改constants.py配置关节名称和参数")
        print(f"2. 修改sim_env.py适配新的机械臂")
        print(f"3. 开始数据收集和训练")
    else:
        print(f"\n❌ 测试失败，请检查XML文件配置。")
        print(f"\n可能的问题:")
        print(f"1. mesh文件路径不正确")
        print(f"2. XML语法错误")
        print(f"3. 惯性参数设置问题")
        print(f"4. 关节配置错误")
