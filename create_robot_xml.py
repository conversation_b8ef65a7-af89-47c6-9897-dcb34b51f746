#!/usr/bin/env python3
"""
从URDF信息创建MuJoCo机械臂XML文件的脚本
"""

def create_robot_xml(robot_name, joints_info, links_info, output_path=None):
    """
    创建机械臂XML文件
    
    Args:
        robot_name: 机械臂名称
        joints_info: 关节信息列表
        links_info: 连杆信息列表
        output_path: 输出路径
    """
    
    if output_path is None:
        output_path = f"assets/{robot_name}.xml"
    
    content = f'''
<mujocoinclude>
    <body name="{robot_name}" pos="0 0 0">
'''
    
    # 递归构建关节和连杆结构
    content += build_kinematic_chain(robot_name, joints_info, links_info, 0, "        ")
    
    content += '''    </body>
</mujocoinclude>'''
    
    with open(output_path, 'w') as f:
        f.write(content)
    
    print(f"Robot XML file created: {output_path}")
    return output_path

def build_kinematic_chain(robot_name, joints_info, links_info, current_joint_idx, indent):
    """递归构建运动链"""
    
    if current_joint_idx >= len(joints_info):
        return ""
    
    joint = joints_info[current_joint_idx]
    link = links_info[current_joint_idx] if current_joint_idx < len(links_info) else None
    
    content = ""
    
    # 添加连杆几何体（如果有mesh）
    if link and link.get('mesh'):
        content += f'{indent}<geom type="mesh" mesh="{link["mesh"]}" name="{robot_name}/{link["name"]}" />\n'
    
    # 如果有下一个关节，创建body和joint
    if current_joint_idx + 1 < len(joints_info):
        next_joint = joints_info[current_joint_idx + 1]
        next_link = links_info[current_joint_idx + 1] if current_joint_idx + 1 < len(links_info) else None
        
        # 关节位置（需要从URDF的origin标签获取）
        pos = joint.get('origin_xyz', '0 0 0')
        
        content += f'{indent}<body name="{robot_name}/{next_link["name"] if next_link else f"link_{current_joint_idx+1}"}" pos="{pos}">\n'
        
        # 惯性信息（从URDF的inertial标签获取）
        if next_link and next_link.get('inertial'):
            inertial = next_link['inertial']
            content += f'{indent}    <inertial pos="{inertial.get("origin_xyz", "0 0 0")}" '
            content += f'mass="{inertial.get("mass", "1.0")}" '
            content += f'diaginertia="{inertial.get("diaginertia", "0.001 0.001 0.001")}" />\n'
        
        # 关节定义
        axis = joint.get('axis', '0 0 1')
        joint_type = joint.get('type', 'revolute')
        limits = joint.get('limits', '-3.14159 3.14159')
        
        if joint_type == 'revolute':
            content += f'{indent}    <joint name="{robot_name}/{joint["name"]}" pos="0 0 0" axis="{axis}" '
            content += f'limited="true" range="{limits}" frictionloss="30" />\n'
        elif joint_type == 'prismatic':
            content += f'{indent}    <joint name="{robot_name}/{joint["name"]}" pos="0 0 0" axis="{axis}" '
            content += f'type="slide" limited="true" range="{limits}" frictionloss="30" />\n'
        
        # 递归处理下一个关节
        content += build_kinematic_chain(robot_name, joints_info, links_info, current_joint_idx + 1, indent + "    ")
        
        content += f'{indent}</body>\n'
    
    return content

# 示例用法和URDF解析辅助函数
def parse_urdf_example():
    """
    示例：如何从URDF提取信息
    你需要根据实际URDF文件填写这些信息
    """
    
    # 从URDF的<joint>标签提取
    joints_info = [
        {
            'name': 'joint1',
            'type': 'revolute',  # 或 'prismatic'
            'axis': '0 0 1',     # 从<axis xyz="..."/>获取
            'limits': '-3.14159 3.14159',  # 从<limit lower="..." upper="..."/>获取
            'origin_xyz': '0 0 0.1',  # 从<origin xyz="..."/>获取
        },
        {
            'name': 'joint2',
            'type': 'revolute',
            'axis': '0 1 0',
            'limits': '-1.57 1.57',
            'origin_xyz': '0 0 0.2',
        },
        # ... 添加更多关节
    ]
    
    # 从URDF的<link>标签提取
    links_info = [
        {
            'name': 'base_link',
            'mesh': 'base_link',  # 对应dependencies.xml中的mesh name
            'inertial': {
                'origin_xyz': '0 0 0',
                'mass': '1.0',
                'diaginertia': '0.001 0.001 0.001'  # 从<inertia ixx="..." iyy="..." izz="..."/>计算
            }
        },
        {
            'name': 'link1',
            'mesh': 'link1',
            'inertial': {
                'origin_xyz': '0 0 0.05',
                'mass': '0.5',
                'diaginertia': '0.0005 0.0005 0.0005'
            }
        },
        # ... 添加更多连杆
    ]
    
    return joints_info, links_info

if __name__ == "__main__":
    robot_name = "your_robot"
    joints_info, links_info = parse_urdf_example()
    
    create_robot_xml(robot_name, joints_info, links_info)
    
    print("""
请根据你的URDF文件修改joints_info和links_info：

1. 关节信息 (从<joint>标签提取):
   - name: 关节名称
   - type: revolute 或 prismatic
   - axis: 旋转轴方向
   - limits: 关节限制范围
   - origin_xyz: 关节位置

2. 连杆信息 (从<link>标签提取):
   - name: 连杆名称
   - mesh: 对应的mesh名称
   - inertial: 惯性参数

3. 检查生成的XML文件并根据需要调整
""")
