#!/usr/bin/env python3
"""
URDF to MuJoCo XML converter script
使用MuJoCo的内置转换功能将URDF转换为MJCF格式
"""

import os
import mujoco
import argparse
from pathlib import Path

def convert_urdf_to_mjcf(urdf_path, output_dir, robot_name):
    """
    将URDF文件转换为MuJoCo XML格式
    
    Args:
        urdf_path: URDF文件路径
        output_dir: 输出目录
        robot_name: 机械臂名称
    """
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    try:
        # 使用MuJoCo加载URDF
        print(f"Loading URDF: {urdf_path}")
        model = mujoco.MjModel.from_xml_path(urdf_path)
        
        # 保存为MJCF格式
        output_path = os.path.join(output_dir, f"{robot_name}.xml")
        mujoco.mj_saveLastXML(output_path, model)
        
        print(f"Converted MJCF saved to: {output_path}")
        
        # 提取mesh信息
        extract_mesh_info(model, output_dir, robot_name)
        
    except Exception as e:
        print(f"自动转换失败: {e}")
        print("请使用手动转换方法")
        return False
    
    return True

def extract_mesh_info(model, output_dir, robot_name):
    """提取mesh信息用于创建dependencies文件"""
    
    mesh_info = []
    for i in range(model.nmesh):
        mesh_name = mujoco.mj_id2name(model, mujoco.mjtObj.mjOBJ_MESH, i)
        if mesh_name:
            # 假设mesh文件在meshes目录下
            mesh_file = f"meshes/{mesh_name}.stl"
            mesh_info.append({
                'name': mesh_name,
                'file': mesh_file
            })
    
    # 创建dependencies文件
    create_dependencies_file(output_dir, robot_name, mesh_info)

def create_dependencies_file(output_dir, robot_name, mesh_info):
    """创建dependencies.xml文件"""
    
    deps_content = '''<mujocoinclude>
    <compiler angle="radian" inertiafromgeom="auto" inertiagrouprange="4 5"/>
    <asset>
'''
    
    for mesh in mesh_info:
        deps_content += f'        <mesh name="{mesh["name"]}" file="{mesh["file"]}" scale="0.001 0.001 0.001" />\n'
    
    deps_content += '''    </asset>
</mujocoinclude>'''
    
    deps_path = os.path.join(output_dir, f"{robot_name}_dependencies.xml")
    with open(deps_path, 'w') as f:
        f.write(deps_content)
    
    print(f"Dependencies file created: {deps_path}")

def main():
    parser = argparse.ArgumentParser(description='Convert URDF to MuJoCo MJCF')
    parser.add_argument('--urdf', required=True, help='Path to URDF file')
    parser.add_argument('--output', default='./assets', help='Output directory')
    parser.add_argument('--name', required=True, help='Robot name')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.urdf):
        print(f"URDF file not found: {args.urdf}")
        return
    
    success = convert_urdf_to_mjcf(args.urdf, args.output, args.name)
    
    if success:
        print("\n转换完成！请检查生成的文件并根据需要进行调整。")
        print_manual_steps(args.name)
    else:
        print("\n自动转换失败，请参考手动转换步骤。")
        print_manual_conversion_guide()

def print_manual_steps(robot_name):
    """打印后续手动调整步骤"""
    print(f"""
后续步骤：
1. 检查生成的 {robot_name}.xml 文件
2. 调整关节限制和摩擦参数
3. 确保mesh文件路径正确
4. 添加相机和传感器（如需要）
5. 在主场景文件中包含这些文件
""")

def print_manual_conversion_guide():
    """打印手动转换指南"""
    print("""
手动转换步骤：
1. 分析URDF文件结构
2. 创建dependencies.xml文件注册mesh
3. 创建机械臂XML文件定义关节和连杆
4. 参考现有的vx300s文件作为模板
""")

if __name__ == "__main__":
    main()
