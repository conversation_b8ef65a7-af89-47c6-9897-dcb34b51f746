# QJ2C双臂机械臂可视化指南

## 🎉 已完成的可视化工具

我已经为你的QJ2C双臂机械臂创建了完整的MuJoCo可视化工具，包括：

### ✅ 1. 相机视图演示 (`demo_qj2c_cameras.py`)
- **功能**: 生成三个主要相机的视图图片
- **输出**: `qj2c_camera_views_demo.png` 
- **特点**: 
  - 显示机械臂在不同相机角度下的视图
  - 标注相机位置和视野信息
  - 无需图形界面，适合服务器环境

### ✅ 2. 交互式3D查看器 (`launch_qj2c_viewer.py`)
- **功能**: 启动MuJoCo交互式图形界面
- **特点**:
  - 实时3D可视化
  - 机械臂动态演示动画
  - 相机位置标记（红/绿/蓝球体）
  - 鼠标交互控制视角

### ✅ 3. 环境测试 (`qj2c_sim_env.py`)
- **功能**: 测试QJ2C环境配置
- **验证**: XML文件、关节控制、状态获取

### ✅ 4. 一键启动脚本 (`run_qj2c_visualization.sh`)
- **功能**: 便捷的启动脚本
- **选项**: 相机演示、交互式查看器、环境测试

## 🚀 使用方法

### 方法1: 使用启动脚本（推荐）
```bash
./run_qj2c_visualization.sh
```

### 方法2: 直接运行Python脚本

#### 📷 相机视图演示（推荐开始）
```bash
conda activate env_RSB
python3 demo_qj2c_cameras.py
```
- ✅ 无需图形界面
- ✅ 生成PNG图片文件
- ✅ 显示三个相机的视角

#### 🎮 交互式3D查看器
```bash
conda activate env_RSB
python3 launch_qj2c_viewer.py
```
- ⚠️ 需要图形界面支持
- ✅ 实时3D可视化
- ✅ 鼠标交互控制

#### 🧪 环境测试
```bash
conda activate env_RSB
python3 qj2c_sim_env.py
```

## 📊 可视化内容

### 🤖 机械臂配置
- **双臂结构**: 左臂 + 右臂
- **关节数**: 每臂7个关节，总共14个
- **显示方式**: 简化几何体（圆柱体、球体）
- **动画**: 自动执行展示动作

### 📷 相机系统
| 相机名称 | 位置 | 用途 | 标记颜色 |
|---------|------|------|----------|
| **top** | [0, 0.6, 0.8] | 俯视角，观察整体布局 | 🔴 红色 |
| **qj2c_front** | [0.8, 0.6, 1.2] | 前视角，观察正面动作 | 🟢 绿色 |
| **qj2c_side** | [0, 1.4, 1.2] | 侧视角，观察侧面动作 | 🔵 蓝色 |

### 🎯 场景元素
- **机械臂基座**: 灰色圆柱体
- **工作台面**: 绿色平面
- **测试立方体**: 红色立方体
- **目标位置**: 绿色方块
- **相机标记**: 彩色球体 + 方向指示

## 🎮 交互式控制

### 鼠标控制
- **左键拖拽**: 旋转视角
- **右键拖拽**: 平移视角  
- **滚轮**: 缩放视角

### 键盘控制
- **空格键**: 暂停/继续仿真
- **Ctrl+R**: 重置仿真
- **Tab**: 切换相机视角
- **ESC**: 退出程序

## 📁 生成的文件

### 图片文件
- `qj2c_camera_views_demo.png` - 三相机视图对比图
- `qj2c_test_render.png` - 测试渲染图

### 配置文件
- `assets/qj2c/qj2c_simple_scene.xml` - 主场景文件
- `assets/qj2c/qj2c_simple.xml` - 机械臂定义
- `assets/qj2c/qj2c_dependencies.xml` - Mesh依赖

## 🔧 故障排除

### 常见问题

1. **图形界面无法启动**
   ```bash
   # 先尝试相机演示模式
   python3 demo_qj2c_cameras.py
   ```

2. **conda环境问题**
   ```bash
   conda activate env_RSB
   conda list | grep mujoco
   ```

3. **XML文件错误**
   ```bash
   python3 qj2c_sim_env.py  # 测试环境
   ```

4. **显示问题**
   - 检查是否有图形界面支持
   - 尝试SSH时使用 `ssh -X` 启用X11转发
   - 在服务器环境使用相机演示模式

### 性能优化

1. **降低渲染质量**（如果运行缓慢）
   - 修改图像分辨率：`height=240, width=320`
   - 减少动画频率

2. **增加相机数量**
   - 在XML文件中添加更多相机
   - 修改Python脚本捕获更多视角

## 🎯 下一步建议

### 1. 验证可视化
```bash
# 首先运行相机演示，确认一切正常
python3 demo_qj2c_cameras.py

# 查看生成的图片
ls -la *.png
```

### 2. 尝试交互式界面
```bash
# 如果有图形界面支持
python3 launch_qj2c_viewer.py
```

### 3. 集成到训练流程
- 在训练过程中使用相机视图验证数据
- 使用可视化工具调试机械臂动作
- 观察不同相机角度的效果

### 4. 自定义扩展
- 添加更多相机角度
- 修改机械臂动画
- 添加更多场景元素

## 📞 技术支持

如果遇到问题：

1. **检查环境**: `conda list | grep mujoco`
2. **测试基础功能**: `python3 qj2c_sim_env.py`
3. **查看错误日志**: 运行时的详细错误信息
4. **尝试简化版本**: 先运行相机演示模式

你的QJ2C双臂机械臂现在有了完整的可视化系统！🎉

可以清楚地看到：
- ✅ 机械臂的结构和动作
- ✅ 三个训练相机的位置和视野
- ✅ 环境布局和物体位置
- ✅ 实时动画演示

这将大大帮助你理解和调试ACT训练过程！
