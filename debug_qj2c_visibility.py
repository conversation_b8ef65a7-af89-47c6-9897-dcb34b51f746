#!/usr/bin/env python3
"""
调试QJ2C机械臂可视性问题
"""

import numpy as np
import mujoco
import matplotlib.pyplot as plt

def debug_qj2c_model():
    """调试QJ2C模型的可视性"""
    
    print("🔍 调试QJ2C机械臂可视性...")
    
    # 加载模型
    model = mujoco.MjModel.from_xml_path('assets/qj2c/qj2_move.xml')
    data = mujoco.MjData(model)
    
    print(f"✅ 模型加载成功")
    print(f"   关节数: {model.njnt}")
    print(f"   执行器数: {model.nu}")
    print(f"   几何体数: {model.ngeom}")
    print(f"   Body数: {model.nbody}")
    
    # 设置机械臂到一个明显的姿态
    if model.nu >= 14:
        # 右臂大幅度动作
        data.ctrl[0] = 1.0    # r_arm_Joint1
        data.ctrl[1] = 0.5    # r_arm_Joint2  
        data.ctrl[2] = 1.5    # r_arm_Joint3
        data.ctrl[3] = -1.0   # r_arm_Joint4
        data.ctrl[4] = 0.8    # r_arm_Joint5
        data.ctrl[5] = 0.5    # r_arm_Joint6
        data.ctrl[6] = 0.3    # r_arm_Joint7
        
        # 左臂大幅度动作
        data.ctrl[7] = -1.0   # l_arm_Joint1
        data.ctrl[8] = 0.5    # l_arm_Joint2
        data.ctrl[9] = -1.5   # l_arm_Joint3
        data.ctrl[10] = -1.0  # l_arm_Joint4
        data.ctrl[11] = -0.8  # l_arm_Joint5
        data.ctrl[12] = -0.5  # l_arm_Joint6
        data.ctrl[13] = -0.3  # l_arm_Joint7
    
    # 运行仿真让机械臂到达目标位置
    for _ in range(200):
        mujoco.mj_step(model, data)
    
    print(f"\n📊 几何体信息:")
    for i in range(model.ngeom):
        geom_name = mujoco.mj_id2name(model, mujoco.mjtObj.mjOBJ_GEOM, i)
        geom_pos = data.geom_xpos[i]
        geom_size = model.geom_size[i]
        geom_type = model.geom_type[i]
        
        type_names = {0: 'plane', 1: 'hfield', 2: 'sphere', 3: 'capsule', 
                     4: 'ellipsoid', 5: 'cylinder', 6: 'box', 7: 'mesh'}
        type_name = type_names.get(geom_type, f'type_{geom_type}')
        
        if geom_name and ('qj2c' in geom_name or 'AR' in geom_name or 'AL' in geom_name):
            print(f"   {geom_name}: {type_name}, 位置[{geom_pos[0]:.3f}, {geom_pos[1]:.3f}, {geom_pos[2]:.3f}], 大小{geom_size}")
    
    print(f"\n📷 相机视角测试:")
    renderer = mujoco.Renderer(model, height=480, width=640)
    
    # 测试不同相机角度
    camera_configs = [
        {'name': 'wide_view', 'pos': [2, 2, 2], 'target': [0, 0, 1]},
        {'name': 'top_view', 'pos': [0, 0, 3], 'target': [0, 0, 0.8]},
        {'name': 'side_view', 'pos': [3, 0, 1], 'target': [0, 0, 0.8]},
        {'name': 'front_view', 'pos': [0, 3, 1], 'target': [0, 0, 0.8]},
    ]
    
    images = {}
    for cam_config in camera_configs:
        # 设置相机
        cam_pos = np.array(cam_config['pos'])
        cam_target = np.array(cam_config['target'])
        
        # 计算相机朝向
        forward = cam_target - cam_pos
        forward = forward / np.linalg.norm(forward)
        
        # 创建临时相机
        renderer.update_scene(data, camera=-1)  # 使用自由相机
        
        # 手动设置相机位置（这里简化处理）
        try:
            image = renderer.render()
            images[cam_config['name']] = image
            print(f"   ✅ {cam_config['name']}: 图像渲染成功")
        except Exception as e:
            print(f"   ❌ {cam_config['name']}: 渲染失败 - {e}")
    
    # 使用现有相机渲染
    print(f"\n📸 使用现有相机渲染:")
    for i in range(model.ncam):
        cam_name = mujoco.mj_id2name(model, mujoco.mjtObj.mjOBJ_CAMERA, i)
        if cam_name:
            try:
                renderer.update_scene(data, camera=i)
                image = renderer.render()
                images[f'cam_{cam_name}'] = image
                print(f"   ✅ {cam_name}: 图像渲染成功")
            except Exception as e:
                print(f"   ❌ {cam_name}: 渲染失败 - {e}")
    
    # 显示图像
    if images:
        print(f"\n🖼️  显示渲染图像...")
        n_images = len(images)
        cols = min(3, n_images)
        rows = (n_images + cols - 1) // cols
        
        fig, axes = plt.subplots(rows, cols, figsize=(15, 5*rows))
        if n_images == 1:
            axes = [axes]
        elif rows == 1:
            axes = axes.reshape(1, -1)
        
        for idx, (name, image) in enumerate(images.items()):
            row = idx // cols
            col = idx % cols
            ax = axes[row, col] if rows > 1 else axes[col]
            
            ax.imshow(image)
            ax.set_title(f'{name}')
            ax.axis('off')
        
        # 隐藏多余的子图
        for idx in range(n_images, rows * cols):
            row = idx // cols
            col = idx % cols
            ax = axes[row, col] if rows > 1 else axes[col]
            ax.axis('off')
        
        plt.tight_layout()
        plt.savefig('qj2c_debug_views.png', dpi=150, bbox_inches='tight')
        print(f"✅ 调试图像已保存: qj2c_debug_views.png")
        plt.show()
    
    # 检查关节位置
    print(f"\n🔧 关节状态:")
    for i in range(model.njnt):
        joint_name = mujoco.mj_id2name(model, mujoco.mjtObj.mjOBJ_JOINT, i)
        if joint_name and 'qj2c' in joint_name:
            joint_pos = data.qpos[i] if i < len(data.qpos) else 0
            print(f"   {joint_name}: {joint_pos:.3f}")
    
    print(f"\n💡 调试建议:")
    print(f"   1. 检查机械臂是否在相机视野内")
    print(f"   2. 调整相机位置和角度")
    print(f"   3. 检查mesh文件的缩放比例")
    print(f"   4. 确认机械臂基座位置 (应该在 z=0.8)")

if __name__ == "__main__":
    debug_qj2c_model()
