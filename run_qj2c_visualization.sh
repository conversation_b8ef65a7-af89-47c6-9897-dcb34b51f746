#!/bin/bash

# QJ2C机械臂可视化启动脚本

echo "🤖 QJ2C双臂机械臂可视化工具"
echo "================================"
echo ""

# 检查conda环境
if ! command -v conda &> /dev/null; then
    echo "❌ 未找到conda，请先安装Miniconda或Anaconda"
    exit 1
fi

# 激活环境
echo "🔧 激活conda环境: env_RSB"
source ~/miniconda3/etc/profile.d/conda.sh
conda activate env_RSB

if [ $? -ne 0 ]; then
    echo "❌ 无法激活环境env_RSB，请检查环境是否存在"
    exit 1
fi

echo "✅ 环境激活成功"
echo ""

# 显示选项
echo "请选择可视化模式:"
echo "1. 📷 相机视图演示 (生成图片，无需图形界面)"
echo "2. 🎮 交互式3D查看器 (需要图形界面支持)"
echo "3. 🧪 环境测试 (测试XML文件和环境配置)"
echo ""

read -p "请输入选择 (1-3，默认1): " choice

case $choice in
    2)
        echo "🎮 启动交互式3D查看器..."
        echo "   - 可以用鼠标旋转、缩放视角"
        echo "   - 可以看到机械臂实时动画"
        echo "   - 红/绿/蓝球体标记相机位置"
        echo ""
        python3 launch_qj2c_viewer.py
        ;;
    3)
        echo "🧪 运行环境测试..."
        python3 qj2c_sim_env.py
        ;;
    *)
        echo "📷 运行相机视图演示..."
        echo "   - 生成三个相机的视图图片"
        echo "   - 显示机械臂和环境布局"
        echo "   - 保存为PNG文件"
        echo ""
        python3 demo_qj2c_cameras.py
        
        if [ -f "qj2c_camera_views_demo.png" ]; then
            echo ""
            echo "✅ 图片已生成: qj2c_camera_views_demo.png"
            echo "🖼️  可以用图片查看器打开查看"
            
            # 尝试自动打开图片
            if command -v xdg-open &> /dev/null; then
                echo "🚀 尝试自动打开图片..."
                xdg-open qj2c_camera_views_demo.png &
            elif command -v open &> /dev/null; then
                echo "🚀 尝试自动打开图片..."
                open qj2c_camera_views_demo.png &
            fi
        fi
        ;;
esac

echo ""
echo "👋 可视化完成"
