# QJ2C双臂机械臂 ACT训练指南

## 🎉 已完成的工作

### ✅ 1. URDF转换和XML配置
- **原始文件**: `assets/qj2c/arms_gen3_urdf.urdf`
- **Mesh文件**: `assets/qj2c/meshes/*.STL` (15个STL文件)
- **生成的配置文件**:
  - `assets/qj2c/qj2c_dependencies.xml` - mesh依赖定义
  - `assets/qj2c/qj2c_simple.xml` - 简化版机械臂定义
  - `assets/qj2c/qj2c_simple_scene.xml` - 完整仿真场景

### ✅ 2. 机械臂规格
- **双臂结构**: 每臂7个关节，总共14个可动关节
- **右臂关节**: r_arm_Joint1 到 r_arm_Joint7
- **左臂关节**: l_arm_Joint1 到 l_arm_Joint7
- **状态维度**: 14 (关节位置) + 14 (关节速度) = 28维观测空间
- **动作维度**: 14 (每个关节的目标位置)

### ✅ 3. 环境配置
- **环境文件**: `qj2c_sim_env.py`
- **任务类型**: 
  - `qj2c_transfer_cube` - 单臂抓取转移任务
  - `qj2c_bimanual_task` - 双臂协作任务
- **相机视角**: top, qj2c_front, qj2c_side

## 🚀 使用方法

### 1. 测试环境
```bash
conda activate env_RSB
python3 qj2c_sim_env.py
```

### 2. 训练QJ2C机械臂

#### 步骤1: 数据收集
首先需要收集演示数据。你可以选择以下方式之一：

**选项A: 创建脚本策略**
```python
# 在scripted_policy.py中添加QJ2C策略
class QJ2CPickAndTransferPolicy:
    def __init__(self):
        pass
    
    def __call__(self, ts):
        # 实现你的脚本策略
        # 返回14维动作向量
        action = np.zeros(14)
        # ... 你的策略逻辑
        return action
```

**选项B: 人工演示**
收集真实的人工演示数据，确保数据格式为HDF5，包含：
- `/observations/qpos` - 关节位置 (timesteps, 14)
- `/observations/qvel` - 关节速度 (timesteps, 14)
- `/observations/images/top` - 顶视图图像
- `/observations/images/qj2c_front` - 前视图图像
- `/action` - 动作序列 (timesteps, 14)

#### 步骤2: 修改训练配置
在`imitate_episodes.py`中修改状态维度：
```python
# 将state_dim从14改为14 (QJ2C的关节数)
state_dim = 14
```

#### 步骤3: 运行训练
```bash
python3 imitate_episodes.py \
    --task_name qj2c_transfer_cube \
    --ckpt_dir ./checkpoints/qj2c_transfer_cube \
    --policy_class ACT \
    --batch_size 8 \
    --seed 0 \
    --num_epochs 2000 \
    --lr 1e-5 \
    --kl_weight 10 \
    --chunk_size 100 \
    --hidden_dim 512 \
    --dim_feedforward 3200
```

#### 步骤4: 评估模型
```bash
python3 imitate_episodes.py \
    --eval \
    --task_name qj2c_transfer_cube \
    --ckpt_dir ./checkpoints/qj2c_transfer_cube \
    --policy_class ACT \
    --batch_size 8 \
    --seed 0
```

### 3. 自定义任务

要创建新的QJ2C任务，在`qj2c_sim_env.py`中添加新的任务类：

```python
class QJ2CCustomTask(QJ2CBaseTask):
    def __init__(self, random=None):
        super().__init__(random=random)
        self.max_reward = 10  # 设置最大奖励

    def initialize_episode(self, physics):
        """初始化episode"""
        self.initialize_robots(physics)
        # 设置你的任务环境
        
    def get_reward(self, physics):
        """计算奖励"""
        # 实现你的奖励函数
        return reward
```

然后在`constants.py`中添加任务配置：
```python
'qj2c_custom_task': {
    'dataset_dir': 'data/qj2c_custom_task',
    'num_episodes': 50,
    'episode_len': 400,
    'camera_names': ['top', 'qj2c_front']
},
```

## 📊 关键参数说明

### 训练参数
- **batch_size**: 8 (根据GPU内存调整)
- **num_epochs**: 2000+ (QJ2C可能需要更长训练时间)
- **lr**: 1e-5 (学习率)
- **kl_weight**: 10 (KL散度权重，控制策略的探索性)
- **chunk_size**: 100 (动作序列长度，影响规划能力)
- **hidden_dim**: 512 (Transformer隐藏维度)

### 环境参数
- **episode_len**: 400-500 (根据任务复杂度调整)
- **camera_names**: ['top', 'qj2c_front'] (可添加更多视角)
- **control_timestep**: 0.02 (20ms控制周期)

## 🔧 故障排除

### 常见问题

1. **XML加载失败**
   - 检查mesh文件路径是否正确
   - 确保所有STL文件都在`assets/qj2c/meshes/`目录下

2. **关节控制异常**
   - 确保动作维度为14
   - 检查关节限制范围是否合理

3. **训练不收敛**
   - 增加训练epochs
   - 调整kl_weight参数
   - 检查数据质量和多样性

4. **内存不足**
   - 减少batch_size
   - 降低图像分辨率
   - 减少相机数量

### 性能优化

1. **提高训练效率**
   - 使用多GPU训练
   - 增加num_workers
   - 使用更快的存储设备

2. **提高策略性能**
   - 收集更多高质量演示数据
   - 使用temporal aggregation
   - 调整网络架构参数

## 📈 下一步扩展

1. **添加真实mesh模型**
   - 解决STL文件兼容性问题
   - 使用原始的详细mesh模型

2. **增加夹爪控制**
   - 为每个臂添加夹爪关节
   - 扩展动作空间到16维

3. **多任务学习**
   - 训练一个模型处理多种任务
   - 使用任务条件化

4. **实际部署**
   - 集成到真实QJ2C机械臂控制系统
   - 添加安全检查和限制

## 📞 支持

如果遇到问题，请检查：
1. 环境配置是否正确
2. 文件路径是否存在
3. 数据格式是否匹配
4. 参数设置是否合理

祝你训练成功！🎯
