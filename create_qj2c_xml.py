#!/usr/bin/env python3
"""
为QJ2C双臂机械臂创建MuJoCo XML文件
"""

def create_qj2c_xml():
    """创建QJ2C双臂机械臂的XML文件"""
    
    # 右臂关节信息 (基于URDF解析结果)
    right_arm_joints = [
        {'name': 'r_arm_Joint1', 'axis': '0 0 1', 'pos': '0 -0.193 0.055', 'range': '-3.14159 3.14159'},
        {'name': 'r_arm_Joint2', 'axis': '0 0 1', 'pos': '0.0565 0 0.067', 'range': '-3.14159 3.14159'},
        {'name': 'r_arm_Joint3', 'axis': '0 0 1', 'pos': '0.059 0 -0.0565', 'range': '-3.14159 3.14159'},
        {'name': 'r_arm_Joint4', 'axis': '0 0 1', 'pos': '-0.0424 0 0.186', 'range': '-3.14159 3.14159'},
        {'name': 'r_arm_Joint5', 'axis': '0 0 1', 'pos': '0 -0.14 -0.0424', 'range': '-3.14159 3.14159'},
        {'name': 'r_arm_Joint6', 'axis': '0 0 1', 'pos': '0 0.0345 0.035', 'range': '-3.14159 3.14159'},
        {'name': 'r_arm_Joint7', 'axis': '0 0 1', 'pos': '0.0345 -0.07 -0.0345', 'range': '-3.14159 3.14159'},
    ]
    
    # 左臂关节信息
    left_arm_joints = [
        {'name': 'l_arm_Joint1', 'axis': '0 0 1', 'pos': '0 0.193 0.055', 'range': '-3.14159 3.14159'},
        {'name': 'l_arm_Joint2', 'axis': '0 0 1', 'pos': '0.0565 0 -0.067', 'range': '-3.14159 3.14159'},
        {'name': 'l_arm_Joint3', 'axis': '0 0 1', 'pos': '0.059 0 0.0565', 'range': '-3.14159 3.14159'},
        {'name': 'l_arm_Joint4', 'axis': '0 0 1', 'pos': '-0.0424 0 -0.186', 'range': '-3.14159 3.14159'},
        {'name': 'l_arm_Joint5', 'axis': '0 0 1', 'pos': '0 -0.14 0.0424', 'range': '-3.14159 3.14159'},
        {'name': 'l_arm_Joint6', 'axis': '0 0 1', 'pos': '0 0.0345 -0.035', 'range': '-3.14159 3.14159'},
        {'name': 'l_arm_Joint7', 'axis': '0 0 1', 'pos': '0.0345 -0.07 0.0345', 'range': '-3.14159 3.14159'},
    ]
    
    # 右臂连杆信息
    right_arm_links = [
        {'name': 'AR1', 'mesh': 'AR1', 'mass': '1.0518', 'inertial_pos': '0.0083594 9.2398E-05 0.062254'},
        {'name': 'AR2', 'mesh': 'AR2', 'mass': '0.28432', 'inertial_pos': '0.031266 -8.259E-06 -0.050803'},
        {'name': 'AR3', 'mesh': 'AR3', 'mass': '2.3857', 'inertial_pos': '-0.0033232 3.5802E-06 0.1286'},
        {'name': 'AR4', 'mesh': 'AR4', 'mass': '1.2408', 'inertial_pos': '-0.00058026 -0.094709 -0.042737'},
        {'name': 'AR5', 'mesh': 'AR5', 'mass': '0.99941', 'inertial_pos': '-1.803E-05 0.0011913 0.034704'},
        {'name': 'AR6', 'mesh': 'AR6', 'mass': '1.0311', 'inertial_pos': '0.0013138 -0.06797 -0.033214'},
        {'name': 'AR7', 'mesh': 'AR7', 'mass': '0.25759', 'inertial_pos': '0.088117 0.0039171 -0.025519'},
    ]
    
    # 左臂连杆信息
    left_arm_links = [
        {'name': 'AL1', 'mesh': 'AL1', 'mass': '1.0518', 'inertial_pos': '0.0083594 -9.2399E-05 -0.062254'},
        {'name': 'AL2', 'mesh': 'AL2', 'mass': '0.28432', 'inertial_pos': '0.031266 8.259E-06 0.050803'},
        {'name': 'AL3', 'mesh': 'AL3', 'mass': '2.3857', 'inertial_pos': '-0.0033246 -7.7807E-05 -0.1286'},
        {'name': 'AL4', 'mesh': 'AL4', 'mass': '1.2408', 'inertial_pos': '-0.00058038 -0.094708 0.042703'},
        {'name': 'AL5', 'mesh': 'AL5', 'mass': '0.99941', 'inertial_pos': '1.803E-05 0.0011913 -0.034704'},
        {'name': 'AL6', 'mesh': 'AL6', 'mass': '1.0311', 'inertial_pos': '0.0013138 -0.067927 0.033214'},
        {'name': 'AL7', 'mesh': 'AL7', 'mass': '0.24463', 'inertial_pos': '0.084008 0.0034769 0.024486'},
    ]
    
    # 生成XML内容
    xml_content = '''
<mujocoinclude>
    <!-- QJ2C双臂机械臂 -->
    <body name="qj2c_base" pos="0 0 0.8">
        <!-- 基座 -->
        <geom type="mesh" mesh="base_link" name="qj2c/base_link" contype="0" conaffinity="0"/>
        <inertial pos="-0.0137906099453444 0.00103375326981413 -0.563088704955717" 
                  mass="54.770449857177" 
                  diaginertia="0.255174678928375 0.233021441171162 0.135494765980168" />
        
        <!-- 右臂 -->
'''
    
    # 添加右臂
    xml_content += build_arm_chain("right", right_arm_joints, right_arm_links, "        ")
    
    # 添加左臂
    xml_content += "\n        <!-- 左臂 -->\n"
    xml_content += build_arm_chain("left", left_arm_joints, left_arm_links, "        ")
    
    xml_content += '''
    </body>
</mujocoinclude>'''
    
    # 保存文件
    output_path = "assets/qj2c/qj2c.xml"
    with open(output_path, 'w') as f:
        f.write(xml_content)
    
    print(f"QJ2C机械臂XML文件已创建: {output_path}")
    return output_path

def build_arm_chain(arm_side, joints, links, indent):
    """构建单个机械臂的运动链"""
    
    content = ""
    
    for i, (joint, link) in enumerate(zip(joints, links)):
        if i == 0:
            # 第一个关节直接连接到基座
            content += f'{indent}<body name="qj2c/{link["name"]}" pos="{joint["pos"]}">\n'
        else:
            # 后续关节
            content += f'{indent}    <body name="qj2c/{link["name"]}" pos="{joint["pos"]}">\n'
            indent += "    "
        
        # 添加惯性信息
        content += f'{indent}    <inertial pos="{link["inertial_pos"]}" mass="{link["mass"]}" diaginertia="0.001 0.001 0.001" />\n'
        
        # 添加关节
        content += f'{indent}    <joint name="qj2c/{joint["name"]}" pos="0 0 0" axis="{joint["axis"]}" '
        content += f'limited="true" range="{joint["range"]}" frictionloss="30" />\n'
        
        # 添加几何体
        content += f'{indent}    <geom type="mesh" mesh="{link["mesh"]}" name="qj2c/{link["name"]}" />\n'
    
    # 添加末端执行器位置
    tool_pos = "0.18 0.055 -0.003" if arm_side == "right" else "0.18 0.055 0.003"
    content += f'{indent}    <body name="qj2c/{arm_side}_tool" pos="{tool_pos}">\n'
    content += f'{indent}        <site pos="0 0 0" size="0.01" type="sphere" name="{arm_side}_tool_site" rgba="1 0 0 0.5"/>\n'
    content += f'{indent}    </body>\n'
    
    # 关闭所有body标签
    for i in range(len(joints)):
        content += f'{indent}</body>\n'
        if i < len(joints) - 1:
            indent = indent[:-4]  # 减少缩进
    
    return content

if __name__ == "__main__":
    create_qj2c_xml()
    print("""
QJ2C双臂机械臂XML文件创建完成！

特点：
- 双臂结构，每臂7个关节
- 总共14个可动关节
- 包含完整的惯性参数
- 使用相对路径引用mesh文件
- 添加了末端执行器位置标记

下一步：
1. 创建场景文件
2. 修改constants.py配置
3. 修改sim_env.py适配新的机械臂
""")
