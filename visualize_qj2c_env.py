#!/usr/bin/env python3
"""
QJ2C机械臂环境的MuJoCo图形界面可视化
显示机械臂、相机位置和视野
"""

import numpy as np
import mujoco
import mujoco.viewer
import time
import os
from threading import Thread
import matplotlib.pyplot as plt

class QJ2CVisualizer:
    def __init__(self, xml_path=None):
        if xml_path is None:
            xml_path = os.path.join('assets', 'qj2c', 'qj2c_simple_scene.xml')
        
        print(f"Loading XML: {xml_path}")
        self.model = mujoco.MjModel.from_xml_path(xml_path)
        self.data = mujoco.MjData(self.model)
        
        # 初始化机械臂位置
        self.reset_robot_pose()
        
        print(f"Model loaded successfully!")
        print(f"- Joints: {self.model.njnt}")
        print(f"- Actuators: {self.model.nu}")
        print(f"- Cameras: {self.model.ncam}")
        print(f"- Bodies: {self.model.nbody}")
        
        # 打印相机信息
        self.print_camera_info()
        
    def reset_robot_pose(self):
        """重置机械臂到初始姿态"""
        # 设置一个有趣的初始姿态，让机械臂更可见
        if self.model.nu >= 14:  # 确保有足够的执行器
            # 右臂姿态
            self.data.ctrl[0] = 0.5    # r_arm_Joint1
            self.data.ctrl[1] = -0.3   # r_arm_Joint2  
            self.data.ctrl[2] = 0.8    # r_arm_Joint3
            self.data.ctrl[3] = -0.5   # r_arm_Joint4
            self.data.ctrl[4] = 0.3    # r_arm_Joint5
            self.data.ctrl[5] = 0.2    # r_arm_Joint6
            self.data.ctrl[6] = 0.0    # r_arm_Joint7
            
            # 左臂姿态（镜像右臂）
            self.data.ctrl[7] = -0.5   # l_arm_Joint1
            self.data.ctrl[8] = -0.3   # l_arm_Joint2
            self.data.ctrl[9] = -0.8   # l_arm_Joint3
            self.data.ctrl[10] = -0.5  # l_arm_Joint4
            self.data.ctrl[11] = -0.3  # l_arm_Joint5
            self.data.ctrl[12] = -0.2  # l_arm_Joint6
            self.data.ctrl[13] = 0.0   # l_arm_Joint7
        
        # 前向动力学计算
        mujoco.mj_forward(self.model, self.data)
        
    def print_camera_info(self):
        """打印相机信息"""
        print(f"\n=== 相机信息 ===")
        for i in range(self.model.ncam):
            cam_name = mujoco.mj_id2name(self.model, mujoco.mjtObj.mjOBJ_CAMERA, i)
            if cam_name:
                cam_pos = self.model.cam_pos[i]
                cam_quat = self.model.cam_quat[i]
                fovy = self.model.cam_fovy[i]
                print(f"相机 {i}: {cam_name}")
                print(f"  位置: [{cam_pos[0]:.3f}, {cam_pos[1]:.3f}, {cam_pos[2]:.3f}]")
                print(f"  四元数: [{cam_quat[0]:.3f}, {cam_quat[1]:.3f}, {cam_quat[2]:.3f}, {cam_quat[3]:.3f}]")
                print(f"  视野角: {fovy:.1f}°")
                print()
    
    def add_camera_visualization(self):
        """在场景中添加相机位置的可视化标记"""
        # 这个函数展示如何获取相机信息，实际的可视化标记需要在XML中定义
        for i in range(self.model.ncam):
            cam_name = mujoco.mj_id2name(self.model, mujoco.mjtObj.mjOBJ_CAMERA, i)
            if cam_name:
                cam_pos = self.model.cam_pos[i]
                print(f"相机 {cam_name} 位置: {cam_pos}")
    
    def animate_robot(self):
        """让机械臂做一些动作演示"""
        t = 0
        while True:
            # 简单的正弦波动作
            amplitude = 0.3
            frequency = 0.5
            
            if self.model.nu >= 14:
                # 右臂动作
                self.data.ctrl[0] = amplitude * np.sin(frequency * t)
                self.data.ctrl[2] = 0.5 + amplitude * np.sin(frequency * t + np.pi/4)
                self.data.ctrl[4] = amplitude * np.sin(frequency * t + np.pi/2)
                
                # 左臂动作（相位相反）
                self.data.ctrl[7] = -amplitude * np.sin(frequency * t)
                self.data.ctrl[9] = -0.5 - amplitude * np.sin(frequency * t + np.pi/4)
                self.data.ctrl[11] = -amplitude * np.sin(frequency * t + np.pi/2)
            
            t += 0.02  # 20ms步长
            time.sleep(0.02)
    
    def capture_camera_views(self):
        """捕获所有相机的视图"""
        camera_images = {}
        
        # 获取主要相机的图像
        camera_names = ['top', 'qj2c_front', 'qj2c_side']
        
        for cam_name in camera_names:
            try:
                # 查找相机ID
                cam_id = None
                for i in range(self.model.ncam):
                    if mujoco.mj_id2name(self.model, mujoco.mjtObj.mjOBJ_CAMERA, i) == cam_name:
                        cam_id = i
                        break
                
                if cam_id is not None:
                    # 渲染图像
                    renderer = mujoco.Renderer(self.model, height=480, width=640)
                    renderer.update_scene(self.data, camera=cam_id)
                    image = renderer.render()
                    camera_images[cam_name] = image
                    print(f"✓ 捕获相机 {cam_name} 的图像: {image.shape}")
                else:
                    print(f"✗ 未找到相机: {cam_name}")
                    
            except Exception as e:
                print(f"✗ 捕获相机 {cam_name} 失败: {e}")
        
        return camera_images
    
    def save_camera_views(self, camera_images, prefix="qj2c_camera"):
        """保存相机视图"""
        fig, axes = plt.subplots(1, len(camera_images), figsize=(15, 5))
        if len(camera_images) == 1:
            axes = [axes]
        
        for i, (cam_name, image) in enumerate(camera_images.items()):
            axes[i].imshow(image)
            axes[i].set_title(f'Camera: {cam_name}')
            axes[i].axis('off')
        
        plt.tight_layout()
        filename = f"{prefix}_views.png"
        plt.savefig(filename, dpi=150, bbox_inches='tight')
        print(f"✓ 相机视图已保存: {filename}")
        plt.show()
    
    def run_interactive_viewer(self):
        """运行交互式查看器"""
        print("\n=== 启动MuJoCo交互式查看器 ===")
        print("控制说明:")
        print("- 鼠标左键拖拽: 旋转视角")
        print("- 鼠标右键拖拽: 平移视角") 
        print("- 鼠标滚轮: 缩放")
        print("- 空格键: 暂停/继续仿真")
        print("- Ctrl+R: 重置仿真")
        print("- 按ESC或关闭窗口退出")
        print()
        
        # 启动动画线程
        animation_thread = Thread(target=self.animate_robot, daemon=True)
        animation_thread.start()
        
        # 启动交互式查看器
        with mujoco.viewer.launch_passive(self.model, self.data) as viewer:
            # 设置初始相机视角
            viewer.cam.azimuth = 45
            viewer.cam.elevation = -20
            viewer.cam.distance = 2.0
            viewer.cam.lookat = [0, 0.6, 0.8]
            
            # 主循环
            while viewer.is_running():
                step_start = time.time()
                
                # 仿真步进
                mujoco.mj_step(self.model, self.data)
                
                # 同步到实时
                time_until_next_step = self.model.opt.timestep - (time.time() - step_start)
                if time_until_next_step > 0:
                    time.sleep(time_until_next_step)
    
    def run_headless_demo(self):
        """无头模式演示，捕获相机视图"""
        print("\n=== 无头模式演示 ===")
        
        # 运行一些仿真步骤
        for i in range(100):
            # 简单动作
            if self.model.nu >= 14:
                self.data.ctrl[0] = 0.3 * np.sin(i * 0.1)
                self.data.ctrl[7] = -0.3 * np.sin(i * 0.1)
            
            mujoco.mj_step(self.model, self.data)
        
        # 捕获相机视图
        camera_images = self.capture_camera_views()
        
        if camera_images:
            self.save_camera_views(camera_images)
        else:
            print("未能捕获任何相机图像")

def main():
    print("=== QJ2C机械臂环境可视化 ===")
    
    try:
        # 创建可视化器
        visualizer = QJ2CVisualizer()
        
        # 选择运行模式
        print("\n运行模式:")
        print("1. 交互式图形界面 (默认)")
        print("2. 无头模式 + 保存相机视图")
        print("如需无头模式，请运行: python3 visualize_qj2c_env.py --headless")
        print()

        import sys
        if "--headless" in sys.argv:
            print("启动无头模式...")
            visualizer.run_headless_demo()
        else:
            print("启动交互式图形界面...")
            visualizer.run_interactive_viewer()
            
    except Exception as e:
        print(f"错误: {e}")
        print("\n可能的解决方案:")
        print("1. 确保已激活正确的conda环境")
        print("2. 检查XML文件路径是否正确")
        print("3. 确保所有依赖包已安装")

if __name__ == "__main__":
    main()
