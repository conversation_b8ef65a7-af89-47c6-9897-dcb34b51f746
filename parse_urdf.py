#!/usr/bin/env python3
"""
解析URDF文件并提取关键信息的脚本
"""

import xml.etree.ElementTree as ET
import argparse
import os

def parse_urdf(urdf_path):
    """
    解析URDF文件并提取关节、连杆和mesh信息
    
    Args:
        urdf_path: URDF文件路径
    
    Returns:
        dict: 包含joints, links, meshes信息的字典
    """
    
    tree = ET.parse(urdf_path)
    root = tree.getroot()
    
    # 提取关节信息
    joints = []
    for joint in root.findall('joint'):
        joint_info = {
            'name': joint.get('name'),
            'type': joint.get('type'),
        }
        
        # 提取轴信息
        axis = joint.find('axis')
        if axis is not None:
            joint_info['axis'] = axis.get('xyz', '0 0 1')
        else:
            joint_info['axis'] = '0 0 1'
        
        # 提取限制信息
        limit = joint.find('limit')
        if limit is not None:
            lower = float(limit.get('lower', '-3.14159'))
            upper = float(limit.get('upper', '3.14159'))
            joint_info['limits'] = f'{lower} {upper}'
        else:
            joint_info['limits'] = '-3.14159 3.14159'
        
        # 提取原点信息
        origin = joint.find('origin')
        if origin is not None:
            joint_info['origin_xyz'] = origin.get('xyz', '0 0 0')
            joint_info['origin_rpy'] = origin.get('rpy', '0 0 0')
        else:
            joint_info['origin_xyz'] = '0 0 0'
            joint_info['origin_rpy'] = '0 0 0'
        
        # 提取父子连杆信息
        parent = joint.find('parent')
        child = joint.find('child')
        if parent is not None:
            joint_info['parent'] = parent.get('link')
        if child is not None:
            joint_info['child'] = child.get('link')
        
        joints.append(joint_info)
    
    # 提取连杆信息
    links = []
    meshes = []
    
    for link in root.findall('link'):
        link_info = {
            'name': link.get('name'),
        }
        
        # 提取惯性信息
        inertial = link.find('inertial')
        if inertial is not None:
            inertial_info = {}
            
            origin = inertial.find('origin')
            if origin is not None:
                inertial_info['origin_xyz'] = origin.get('xyz', '0 0 0')
            else:
                inertial_info['origin_xyz'] = '0 0 0'
            
            mass = inertial.find('mass')
            if mass is not None:
                inertial_info['mass'] = mass.get('value', '1.0')
            else:
                inertial_info['mass'] = '1.0'
            
            inertia = inertial.find('inertia')
            if inertia is not None:
                ixx = float(inertia.get('ixx', '0.001'))
                iyy = float(inertia.get('iyy', '0.001'))
                izz = float(inertia.get('izz', '0.001'))
                inertial_info['diaginertia'] = f'{ixx} {iyy} {izz}'
            else:
                inertial_info['diaginertia'] = '0.001 0.001 0.001'
            
            link_info['inertial'] = inertial_info
        
        # 提取视觉mesh信息
        visual = link.find('visual')
        if visual is not None:
            geometry = visual.find('geometry')
            if geometry is not None:
                mesh = geometry.find('mesh')
                if mesh is not None:
                    mesh_filename = mesh.get('filename')
                    if mesh_filename:
                        # 提取文件名（去掉路径和package://前缀）
                        mesh_name = os.path.basename(mesh_filename).replace('.stl', '').replace('.dae', '')
                        link_info['mesh'] = mesh_name
                        
                        # 添加到mesh列表
                        mesh_info = {
                            'name': mesh_name,
                            'file': os.path.basename(mesh_filename)
                        }
                        
                        # 提取缩放信息
                        scale = mesh.get('scale')
                        if scale:
                            mesh_info['scale'] = scale.replace(' ', ' ')
                        else:
                            mesh_info['scale'] = '0.001 0.001 0.001'  # 默认从mm转m
                        
                        meshes.append(mesh_info)
        
        links.append(link_info)
    
    return {
        'joints': joints,
        'links': links,
        'meshes': meshes
    }

def print_parsed_info(parsed_info):
    """打印解析的信息"""
    
    print("=== 关节信息 ===")
    for i, joint in enumerate(parsed_info['joints']):
        print(f"关节 {i+1}: {joint['name']}")
        print(f"  类型: {joint['type']}")
        print(f"  轴: {joint['axis']}")
        print(f"  限制: {joint['limits']}")
        print(f"  位置: {joint['origin_xyz']}")
        if 'parent' in joint and 'child' in joint:
            print(f"  连接: {joint['parent']} -> {joint['child']}")
        print()
    
    print("=== 连杆信息 ===")
    for i, link in enumerate(parsed_info['links']):
        print(f"连杆 {i+1}: {link['name']}")
        if 'mesh' in link:
            print(f"  Mesh: {link['mesh']}")
        if 'inertial' in link:
            inertial = link['inertial']
            print(f"  质量: {inertial['mass']}")
            print(f"  惯性: {inertial['diaginertia']}")
        print()
    
    print("=== Mesh文件 ===")
    for i, mesh in enumerate(parsed_info['meshes']):
        print(f"Mesh {i+1}: {mesh['name']} -> {mesh['file']}")
        print(f"  缩放: {mesh['scale']}")
        print()

def generate_conversion_code(parsed_info, robot_name):
    """生成转换代码"""
    
    print(f"\n=== 为 {robot_name} 生成的转换代码 ===")
    
    # 生成joints_info代码
    print("\n# 关节信息")
    print("joints_info = [")
    for joint in parsed_info['joints']:
        if joint['type'] in ['revolute', 'prismatic']:  # 只处理可动关节
            print(f"    {{")
            print(f"        'name': '{joint['name']}',")
            print(f"        'type': '{joint['type']}',")
            print(f"        'axis': '{joint['axis']}',")
            print(f"        'limits': '{joint['limits']}',")
            print(f"        'origin_xyz': '{joint['origin_xyz']}',")
            print(f"    }},")
    print("]")
    
    # 生成links_info代码
    print("\n# 连杆信息")
    print("links_info = [")
    for link in parsed_info['links']:
        print(f"    {{")
        print(f"        'name': '{link['name']}',")
        if 'mesh' in link:
            print(f"        'mesh': '{link['mesh']}',")
        if 'inertial' in link:
            inertial = link['inertial']
            print(f"        'inertial': {{")
            print(f"            'origin_xyz': '{inertial['origin_xyz']}',")
            print(f"            'mass': '{inertial['mass']}',")
            print(f"            'diaginertia': '{inertial['diaginertia']}'")
            print(f"        }}")
        print(f"    }},")
    print("]")
    
    # 生成mesh_files代码
    print("\n# Mesh文件信息")
    print("mesh_files = [")
    for mesh in parsed_info['meshes']:
        print(f"    {{'name': '{mesh['name']}', 'file': '{mesh['file']}', 'scale': '{mesh['scale']}'}},")
    print("]")

def main():
    parser = argparse.ArgumentParser(description='Parse URDF file and extract information')
    parser.add_argument('--urdf', required=True, help='Path to URDF file')
    parser.add_argument('--robot_name', default='your_robot', help='Robot name')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.urdf):
        print(f"URDF file not found: {args.urdf}")
        return
    
    try:
        parsed_info = parse_urdf(args.urdf)
        print_parsed_info(parsed_info)
        generate_conversion_code(parsed_info, args.robot_name)
        
        print(f"\n=== 下一步操作 ===")
        print(f"1. 将上面生成的代码复制到 create_robot_xml.py 和 create_robot_dependencies.py")
        print(f"2. 将mesh文件复制到 assets/ 目录")
        print(f"3. 运行转换脚本生成XML文件")
        print(f"4. 检查并调整生成的XML文件")
        
    except Exception as e:
        print(f"解析URDF文件时出错: {e}")

if __name__ == "__main__":
    main()
